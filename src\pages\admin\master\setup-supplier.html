<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Set Up Supplier - Rainbow Station Inc</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: #f9fafb;
            color: #1f2937;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            height: 100vh;
            overflow: hidden;
        }

        .page-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* Header */
        .main-header {
            background-color: #ffffff;
            border-bottom: 2px solid #e5e7eb;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            flex-shrink: 0;
            padding: 16px 24px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title h1 {
            font-size: 24px;
            font-weight: 900;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .header-title p {
            font-size: 14px;
            color: #6b7280;
        }

        .header-info {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .header-info-text {
            display: flex;
            flex-direction: column;
            gap: 4px;
            font-size: 14px;
            color: #374151;
        }

        .header-controls {
            display: flex;
            gap: 8px;
        }

        .header-control-btn {
            background-color: #374151;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: background-color 0.2s;
        }

        .header-control-btn:hover {
            background-color: #4b5563;
        }

        .header-control-btn.minimize:hover {
            background-color: #3b82f6;
        }

        .header-control-btn.close:hover {
            background-color: #ef4444;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            overflow-y: auto;
            padding: 24px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 900;
            color: #1f2937;
            margin-bottom: 24px;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
            margin-bottom: 24px;
        }

        .btn {
            padding: 8px 32px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .btn-green {
            background-color: #059669;
            color: white;
        }

        .btn-green:hover {
            background-color: #047857;
        }

        .btn-blue {
            background-color: #2563eb;
            color: white;
        }

        .btn-blue:hover {
            background-color: #1d4ed8;
        }

        .btn-red {
            background-color: #dc2626;
            color: white;
        }

        .btn-red:hover {
            background-color: #b91c1c;
        }

        .form-card {
            background-color: #ffffff;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            padding: 24px;
            margin-bottom: 32px;
        }

        .form-grid {
            display: grid;
            gap: 24px;
        }

        .form-row {
            display: grid;
            gap: 16px;
        }

        .form-row-1 {
            grid-template-columns: 1fr;
        }

        .form-row-2 {
            grid-template-columns: repeat(2, 1fr);
        }

        .form-row-3 {
            grid-template-columns: repeat(3, 1fr);
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-size: 14px;
            font-weight: bold;
            color: #374151;
            margin-bottom: 8px;
        }

        .form-input {
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s, box-shadow 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #059669;
            box-shadow: 0 0 0 2px rgba(5, 150, 105, 0.2);
        }

        .table-card {
            background-color: #ffffff;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            padding: 24px;
        }

        .table-container {
            border: 1px solid #d1d5db;
            border-radius: 8px;
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background-color: #2563eb;
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: bold;
            border-right: 1px solid #1d4ed8;
        }

        .table th:last-child {
            border-right: none;
        }

        .table th.text-center {
            text-align: center;
        }

        .table td {
            padding: 8px 12px;
            border-bottom: 1px solid #e5e7eb;
            border-right: 1px solid #d1d5db;
            color: #1f2937;
        }

        .table td:last-child {
            border-right: none;
        }

        .table tr:hover {
            background-color: #f9fafb;
            cursor: pointer;
        }

        .table tr.selected {
            background-color: #dbeafe;
        }

        .table td.font-medium {
            font-weight: 500;
        }

        .table td.text-center {
            text-align: center;
        }

        .table .form-input {
            width: 100%;
            text-align: center;
            padding: 4px 8px;
            border: 1px solid #d1d5db;
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- Header -->
        <div class="main-header">
            <div class="header-content">
                <div class="header-title">
                    <h1>SET UP SUPPLIER</h1>
                    <p>Rainbow Station Inc.</p>
                </div>
                <div class="header-info">
                    <div class="header-info-text">
                        <div>Date: <span id="current-date">01/01/24</span></div>
                        <div>Time: <span id="current-time">00:00:00</span></div>
                        <div>Admin: <span>Simon</span></div>
                    </div>
                    <div class="header-controls">
                        <button onclick="navigateToAdmin()" class="header-control-btn" style="background-color: #059669;" title="Back to Admin">← ADMIN</button>
                        <button onclick="toggleFullscreen()" id="fullscreen-btn" class="header-control-btn" title="Maximize Window">⛶</button>
                        <button onclick="minimizeApp()" class="header-control-btn minimize" title="Minimize">−</button>
                        <button onclick="closeApp()" class="header-control-btn close" title="Close">×</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Action Buttons -->
            <div class="action-buttons">
                <button class="btn btn-green" onclick="saveSupplier()">SAVE</button>
                <button class="btn btn-blue" onclick="clearForm()">CLEAR</button>
                <button class="btn btn-red" onclick="deleteSupplier()">DELETE</button>
            </div>

            <!-- Main Form -->
            <div class="form-card">
                <div class="form-grid">
                    <!-- Name Row -->
                    <div class="form-row form-row-1">
                        <div class="form-group">
                            <label class="form-label">Name</label>
                            <input type="text" class="form-input" id="name" placeholder="Enter supplier name">
                        </div>
                    </div>

                    <!-- Address Fields -->
                    <div class="form-row form-row-1">
                        <div class="form-group">
                            <label class="form-label">Address1</label>
                            <input type="text" class="form-input" id="address1" placeholder="Enter address line 1">
                        </div>
                    </div>
                    <div class="form-row form-row-1">
                        <div class="form-group">
                            <label class="form-label">Address2</label>
                            <input type="text" class="form-input" id="address2" placeholder="Enter address line 2 (optional)">
                        </div>
                    </div>

                    <!-- City, State, ZIP Row -->
                    <div class="form-row form-row-3">
                        <div class="form-group">
                            <label class="form-label">City</label>
                            <input type="text" class="form-input" id="city" placeholder="Enter city">
                        </div>
                        <div class="form-group">
                            <label class="form-label">State</label>
                            <input type="text" class="form-input" id="state" placeholder="Enter state">
                        </div>
                        <div class="form-group">
                            <label class="form-label">ZIP Code</label>
                            <input type="text" class="form-input" id="zipCode" placeholder="Enter ZIP code">
                        </div>
                    </div>

                    <!-- Contact Information Row -->
                    <div class="form-row form-row-3">
                        <div class="form-group">
                            <label class="form-label">Telephone</label>
                            <input type="tel" class="form-input" id="telephone" placeholder="Enter telephone">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Fax</label>
                            <input type="tel" class="form-input" id="fax" placeholder="Enter fax number">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Email</label>
                            <input type="email" class="form-input" id="email" placeholder="Enter email address">
                        </div>
                    </div>

                    <!-- Sales Rep Information Row -->
                    <div class="form-row form-row-2">
                        <div class="form-group">
                            <label class="form-label">Sale's Rep</label>
                            <input type="text" class="form-input" id="salesRep" placeholder="Enter sales representative name">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Sale's Rep Phone</label>
                            <input type="tel" class="form-input" id="salesRepPhone" placeholder="Enter sales rep phone">
                        </div>
                    </div>

                    <!-- Website Information Row -->
                    <div class="form-row form-row-2">
                        <div class="form-group">
                            <label class="form-label">Retail Website</label>
                            <input type="url" class="form-input" id="retailWebsite" placeholder="Enter retail website URL">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Wholesale Website</label>
                            <input type="url" class="form-input" id="wholesaleWebsite" placeholder="Enter wholesale website URL">
                        </div>
                    </div>

                    <!-- Login Credentials Row -->
                    <div class="form-row form-row-2">
                        <div class="form-group">
                            <label class="form-label">Username</label>
                            <input type="text" class="form-input" id="username" placeholder="Enter username">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Password</label>
                            <input type="password" class="form-input" id="password" placeholder="Enter password">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Supplier Directory Table -->
            <div class="table-card">
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Supplier Name</th>
                                <th class="text-center">Telephone</th>
                                <th class="text-center">Fax</th>
                                <th class="text-center">Email</th>
                                <th class="text-center">Sale's Rep</th>
                                <th class="text-center">Rep Phone #</th>
                            </tr>
                        </thead>
                        <tbody id="supplierTableBody">
                            <!-- Table rows will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="setup-supplier.js"></script>
</body>
</html>
