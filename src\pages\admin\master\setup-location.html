<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Set Up Location - Rainbow Station Inc</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: #f9fafb;
            color: #1f2937;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            height: 100vh;
            overflow: hidden;
        }

        .page-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* Header */
        .main-header {
            background-color: #ffffff;
            border-bottom: 2px solid #e5e7eb;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            flex-shrink: 0;
            padding: 16px 24px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title h1 {
            font-size: 24px;
            font-weight: 900;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .header-title p {
            font-size: 14px;
            color: #6b7280;
        }

        .header-info {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .header-info-text {
            display: flex;
            flex-direction: column;
            gap: 4px;
            font-size: 14px;
            color: #374151;
        }

        .header-controls {
            display: flex;
            gap: 8px;
        }

        .header-control-btn {
            background-color: #374151;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: background-color 0.2s;
        }

        .header-control-btn:hover {
            background-color: #4b5563;
        }

        .header-control-btn.minimize:hover {
            background-color: #3b82f6;
        }

        .header-control-btn.close:hover {
            background-color: #ef4444;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            overflow-y: auto;
            padding: 24px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 900;
            color: #1f2937;
            margin-bottom: 24px;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
            margin-bottom: 24px;
        }

        .btn {
            padding: 8px 32px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .btn-green {
            background-color: #059669;
            color: white;
        }

        .btn-green:hover {
            background-color: #047857;
        }

        .btn-blue {
            background-color: #2563eb;
            color: white;
        }

        .btn-blue:hover {
            background-color: #1d4ed8;
        }

        .btn-red {
            background-color: #dc2626;
            color: white;
        }

        .btn-red:hover {
            background-color: #b91c1c;
        }

        .form-card {
            background-color: #ffffff;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            padding: 24px;
            margin-bottom: 32px;
        }

        .form-grid {
            display: grid;
            gap: 24px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-size: 14px;
            font-weight: bold;
            color: #374151;
            margin-bottom: 8px;
        }

        .form-input, .form-select {
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s, box-shadow 0.2s;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #059669;
            box-shadow: 0 0 0 2px rgba(5, 150, 105, 0.2);
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
            padding-top: 32px;
        }

        .checkbox {
            width: 16px;
            height: 16px;
        }

        .table-card {
            background-color: #ffffff;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            padding: 24px;
        }

        .table-container {
            border: 1px solid #d1d5db;
            border-radius: 8px;
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background-color: #2563eb;
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: bold;
            border-right: 1px solid #1d4ed8;
        }

        .table th:last-child {
            border-right: none;
        }

        .table td {
            padding: 12px;
            border-bottom: 1px solid #e5e7eb;
            border-right: 1px solid #e5e7eb;
            color: #1f2937;
        }

        .table td:last-child {
            border-right: none;
        }

        .table tr:nth-child(even) {
            background-color: #f9fafb;
        }

        .table tr:hover {
            background-color: #f3f4f6;
            cursor: pointer;
        }

        .table td.font-medium {
            font-weight: 500;
        }

        .table td.text-center {
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- Header -->
        <div class="main-header">
            <div class="header-content">
                <div class="header-title">
                    <h1>SET UP LOCATION</h1>
                    <p>Rainbow Station Inc.</p>
                </div>
                <div class="header-info">
                    <div class="header-info-text">
                        <div>Date: <span id="current-date">01/01/24</span></div>
                        <div>Time: <span id="current-time">00:00:00</span></div>
                        <div>Admin: <span>Simon</span></div>
                    </div>
                    <div class="header-controls">
                        <button onclick="navigateToAdmin()" class="header-control-btn" style="background-color: #059669;" title="Back to Admin">← ADMIN</button>
                        <button onclick="toggleFullscreen()" id="fullscreen-btn" class="header-control-btn" title="Maximize Window">⛶</button>
                        <button onclick="minimizeApp()" class="header-control-btn minimize" title="Minimize">−</button>
                        <button onclick="closeApp()" class="header-control-btn close" title="Close">×</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Action Buttons -->
            <div class="action-buttons">
                <button class="btn btn-green" onclick="saveLocation()">SAVE</button>
                <button class="btn btn-blue" onclick="clearForm()">CLEAR</button>
                <button class="btn btn-red" onclick="deleteLocation()">DELETE</button>
            </div>

            <!-- Main Form -->
            <div class="form-card">
                <div class="form-grid">
                    <!-- Location Code and Location Row -->
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Location Code</label>
                            <input type="text" class="form-input" id="locationCode" placeholder="Enter location code">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Location</label>
                            <input type="text" class="form-input" id="location" placeholder="Enter location name">
                        </div>
                    </div>

                    <!-- Company Name and E-mail Row -->
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Company Name</label>
                            <input type="text" class="form-input" id="companyName" placeholder="Enter company name">
                        </div>
                        <div class="form-group">
                            <label class="form-label">E-mail</label>
                            <input type="email" class="form-input" id="email" placeholder="Enter email address">
                        </div>
                    </div>

                    <!-- Address1 and App Mode Row -->
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Address1</label>
                            <input type="text" class="form-input" id="address1" placeholder="Enter address line 1">
                        </div>
                        <div class="form-group">
                            <label class="form-label">App Mode</label>
                            <select class="form-select" id="appMode">
                                <option value="">Select App Mode</option>
                                <option value="retail">Retail</option>
                                <option value="wholesale">Wholesale</option>
                                <option value="both">Both</option>
                            </select>
                        </div>
                    </div>

                    <!-- Address2 and Theater PLU Row -->
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Address2</label>
                            <input type="text" class="form-input" id="address2" placeholder="Enter address line 2">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Theater PLU</label>
                            <input type="text" class="form-input" id="theaterPLU" placeholder="Enter theater PLU">
                        </div>
                    </div>

                    <!-- Phone and Theater Time Row -->
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Phone</label>
                            <input type="tel" class="form-input" id="phone" placeholder="Enter phone number">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Theater Time</label>
                            <input type="text" class="form-input" id="theaterTime" placeholder="Enter theater time">
                        </div>
                    </div>

                    <!-- Tax% and Deli Row -->
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Tax%</label>
                            <input type="number" step="0.001" class="form-input" id="taxPercent" placeholder="Enter tax percentage">
                        </div>
                        <div class="checkbox-group">
                            <input type="checkbox" class="checkbox" id="deli">
                            <label class="form-label" for="deli">Deli</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Location Directory Table -->
            <div class="table-card">
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Location Code</th>
                                <th>Location</th>
                                <th>Company Name</th>
                                <th>Address1</th>
                                <th>Address2</th>
                                <th class="text-center">Phone#</th>
                                <th class="text-center">Tax%</th>
                            </tr>
                        </thead>
                        <tbody id="locationTableBody">
                            <!-- Table rows will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="setup-location.js"></script>
</body>
</html>
