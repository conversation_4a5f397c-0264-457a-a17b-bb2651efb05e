const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// Sample location stock data
let locationStocks = [
    { location: "Taboo-Atlantic City-NJ", stock: 0, price: 0.00 },
    { location: "Huntington", stock: 0, price: 0.00 },
    { location: "Miami Playground-Florida", stock: 0, price: 0.00 },
    { location: "LOVE TOYS", stock: 0, price: 0.00 },
    { location: "Pompano Beach-Florida", stock: 0, price: 0.00 },
    { location: "Rainbow Station-New York", stock: 0, price: 0.00 }
];

// Form data object
let formData = {
    barcode: "",
    description: "",
    category: "",
    subCategory: "",
    supplier: "",
    purchasePrice: "",
    style: "",
    color: "",
    size: "",
    minQty: "",
    maxQty: "",
    specialDiscount: false,
    priority: false,
    imageConfirm: false,
    nonScanable: false,
    deliItem: false
};

// Initialize the application
document.addEventListener('DOMContentLoaded', async function() {
    updateTime();
    setInterval(updateTime, 1000);
    renderLocationStockTable();

    // Initialize maximize button state
    try {
        const isMaximized = await ipcRenderer.invoke('is-fullscreen');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = isMaximized ? '🗗' : '⛶';
            fullscreenBtn.title = isMaximized ? 'Restore Window' : 'Maximize Window';
        }
    } catch (error) {
        console.error('Error checking initial maximize state:', error);
    }
});

// Time functions
function updateTime() {
    const now = new Date();
    document.getElementById('current-date').textContent = formatDate(now);
    document.getElementById('current-time').textContent = formatTime(now);
}

function formatDate(date) {
    return date.toLocaleDateString("en-US", { month: "2-digit", day: "2-digit", year: "2-digit" });
}

function formatTime(date) {
    return date.toLocaleTimeString("en-US", { hour12: false });
}

// Navigation functions
function navigateToAdmin() {
    ipcRenderer.invoke('navigate-to', 'admin');
}

// Window control functions
function closeApp() {
    ipcRenderer.invoke('close-app');
}

function minimizeApp() {
    ipcRenderer.invoke('minimize-app');
}

async function toggleFullscreen() {
    try {
        const isMaximized = await ipcRenderer.invoke('toggle-fullscreen');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = isMaximized ? '🗗' : '⛶';
            fullscreenBtn.title = isMaximized ? 'Restore Window' : 'Maximize Window';
        }
    } catch (error) {
        console.error('Error toggling maximize:', error);
    }
}

// Form functions
function getFormData() {
    return {
        barcode: document.getElementById('barcode').value,
        description: document.getElementById('description').value,
        category: document.getElementById('category').value,
        subCategory: document.getElementById('subCategory').value,
        supplier: document.getElementById('supplier').value,
        purchasePrice: document.getElementById('purchasePrice').value,
        style: document.getElementById('style').value,
        color: document.getElementById('color').value,
        size: document.getElementById('size').value,
        minQty: document.getElementById('minQty').value,
        maxQty: document.getElementById('maxQty').value,
        specialDiscount: document.getElementById('specialDiscount').checked,
        priority: document.getElementById('priority').checked,
        imageConfirm: document.getElementById('imageConfirm').checked,
        nonScanable: document.getElementById('nonScanable').checked,
        deliItem: document.getElementById('deliItem').checked
    };
}

function setFormData(data) {
    document.getElementById('barcode').value = data.barcode || '';
    document.getElementById('description').value = data.description || '';
    document.getElementById('category').value = data.category || '';
    document.getElementById('subCategory').value = data.subCategory || '';
    document.getElementById('supplier').value = data.supplier || '';
    document.getElementById('purchasePrice').value = data.purchasePrice || '';
    document.getElementById('style').value = data.style || '';
    document.getElementById('color').value = data.color || '';
    document.getElementById('size').value = data.size || '';
    document.getElementById('minQty').value = data.minQty || '';
    document.getElementById('maxQty').value = data.maxQty || '';
    document.getElementById('specialDiscount').checked = data.specialDiscount || false;
    document.getElementById('priority').checked = data.priority || false;
    document.getElementById('imageConfirm').checked = data.imageConfirm || false;
    document.getElementById('nonScanable').checked = data.nonScanable || false;
    document.getElementById('deliItem').checked = data.deliItem || false;
}

function saveProduct() {
    const data = getFormData();
    
    if (!data.barcode || !data.description) {
        alert('Please fill in required fields: Barcode and Description');
        return;
    }

    // Get location stock data
    const stockData = [];
    const stockInputs = document.querySelectorAll('#locationStockTable input[data-type="stock"]');
    const priceInputs = document.querySelectorAll('#locationStockTable input[data-type="price"]');
    
    stockInputs.forEach((input, index) => {
        stockData.push({
            location: locationStocks[index].location,
            stock: parseInt(input.value) || 0,
            price: parseFloat(priceInputs[index].value) || 0.00
        });
    });

    const productData = {
        ...data,
        locationStocks: stockData
    };

    console.log('Product saved:', productData);
    alert('Product saved successfully!');
}

function clearForm() {
    setFormData({});
    
    // Clear location stock table
    const stockInputs = document.querySelectorAll('#locationStockTable input[data-type="stock"]');
    const priceInputs = document.querySelectorAll('#locationStockTable input[data-type="price"]');
    
    stockInputs.forEach(input => input.value = '0');
    priceInputs.forEach(input => input.value = '0.00');
    
    console.log('Form cleared');
}

function deleteProduct() {
    const barcode = document.getElementById('barcode').value;
    
    if (!barcode) {
        alert('Please enter a barcode to delete');
        return;
    }

    if (confirm('Are you sure you want to delete this product?')) {
        clearForm();
        console.log('Product deleted:', barcode);
    }
}

function renderLocationStockTable() {
    const tbody = document.getElementById('locationStockTable');
    tbody.innerHTML = '';

    locationStocks.forEach((location, index) => {
        const row = document.createElement('tr');
        
        row.innerHTML = `
            <td class="font-medium">${location.location}</td>
            <td class="text-center">
                <input type="number" class="form-input" data-type="stock" value="${location.stock}" placeholder="0">
            </td>
            <td class="text-center">
                <input type="number" step="0.01" class="form-input price" data-type="price" value="${location.price.toFixed(2)}" placeholder="0.00">
            </td>
        `;
        
        tbody.appendChild(row);
    });
}

// Update location stock when inputs change
document.addEventListener('input', function(e) {
    if (e.target.dataset.type === 'stock') {
        const index = Array.from(document.querySelectorAll('input[data-type="stock"]')).indexOf(e.target);
        if (index >= 0) {
            locationStocks[index].stock = parseInt(e.target.value) || 0;
        }
    } else if (e.target.dataset.type === 'price') {
        const index = Array.from(document.querySelectorAll('input[data-type="price"]')).indexOf(e.target);
        if (index >= 0) {
            locationStocks[index].price = parseFloat(e.target.value) || 0.00;
        }
    }
});
