const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// Sample supplier data
let suppliers = [
    {
        supplierName: "505 INC Distribution center",
        telephone: "",
        fax: "",
        email: "",
        salesRep: "",
        repPhone: ""
    },
    {
        supplierName: "ALLEN BROTHERS",
        telephone: "",
        fax: "",
        email: "",
        salesRep: "",
        repPhone: ""
    },
    {
        supplierName: "Baci",
        telephone: "",
        fax: "",
        email: "",
        salesRep: "",
        repPhone: ""
    },
    {
        supplierName: "Bamboo Magic",
        telephone: "",
        fax: "",
        email: "",
        salesRep: "",
        repPhone: ""
    },
    {
        supplierName: "BE WICKED!",
        telephone: "",
        fax: "",
        email: "",
        salesRep: "",
        repPhone: ""
    },
    {
        supplierName: "Black Unicorn",
        telephone: "",
        fax: "",
        email: "",
        salesRep: "",
        repPhone: ""
    },
    {
        supplierName: "BLUE OX DESIGNS LLC",
        telephone: "",
        fax: "",
        email: "",
        salesRep: "",
        repPhone: ""
    },
    {
        supplierName: "Boy butter",
        telephone: "",
        fax: "",
        email: "",
        salesRep: "",
        repPhone: ""
    },
    {
        supplierName: "Cents Price",
        telephone: "",
        fax: "",
        email: "",
        salesRep: "",
        repPhone: ""
    }
];

// Form data object
let formData = {
    name: "",
    address1: "",
    address2: "",
    city: "",
    state: "",
    zipCode: "",
    telephone: "",
    fax: "",
    email: "",
    salesRep: "",
    salesRepPhone: "",
    retailWebsite: "",
    wholesaleWebsite: "",
    username: "",
    password: ""
};

let selectedSupplierIndex = -1;

// Initialize the application
document.addEventListener('DOMContentLoaded', async function() {
    updateTime();
    setInterval(updateTime, 1000);
    renderSupplierTable();

    // Initialize maximize button state
    try {
        const isMaximized = await ipcRenderer.invoke('is-fullscreen');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = isMaximized ? '🗗' : '⛶';
            fullscreenBtn.title = isMaximized ? 'Restore Window' : 'Maximize Window';
        }
    } catch (error) {
        console.error('Error checking initial maximize state:', error);
    }
});

// Time functions
function updateTime() {
    const now = new Date();
    document.getElementById('current-date').textContent = formatDate(now);
    document.getElementById('current-time').textContent = formatTime(now);
}

function formatDate(date) {
    return date.toLocaleDateString("en-US", { month: "2-digit", day: "2-digit", year: "2-digit" });
}

function formatTime(date) {
    return date.toLocaleTimeString("en-US", { hour12: false });
}

// Navigation functions
function navigateToAdmin() {
    ipcRenderer.invoke('navigate-to', 'admin');
}

// Window control functions
function closeApp() {
    ipcRenderer.invoke('close-app');
}

function minimizeApp() {
    ipcRenderer.invoke('minimize-app');
}

async function toggleFullscreen() {
    try {
        const isMaximized = await ipcRenderer.invoke('toggle-fullscreen');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = isMaximized ? '🗗' : '⛶';
            fullscreenBtn.title = isMaximized ? 'Restore Window' : 'Maximize Window';
        }
    } catch (error) {
        console.error('Error toggling maximize:', error);
    }
}

// Form functions
function getFormData() {
    return {
        name: document.getElementById('name').value,
        address1: document.getElementById('address1').value,
        address2: document.getElementById('address2').value,
        city: document.getElementById('city').value,
        state: document.getElementById('state').value,
        zipCode: document.getElementById('zipCode').value,
        telephone: document.getElementById('telephone').value,
        fax: document.getElementById('fax').value,
        email: document.getElementById('email').value,
        salesRep: document.getElementById('salesRep').value,
        salesRepPhone: document.getElementById('salesRepPhone').value,
        retailWebsite: document.getElementById('retailWebsite').value,
        wholesaleWebsite: document.getElementById('wholesaleWebsite').value,
        username: document.getElementById('username').value,
        password: document.getElementById('password').value
    };
}

function setFormData(data) {
    document.getElementById('name').value = data.name || '';
    document.getElementById('address1').value = data.address1 || '';
    document.getElementById('address2').value = data.address2 || '';
    document.getElementById('city').value = data.city || '';
    document.getElementById('state').value = data.state || '';
    document.getElementById('zipCode').value = data.zipCode || '';
    document.getElementById('telephone').value = data.telephone || '';
    document.getElementById('fax').value = data.fax || '';
    document.getElementById('email').value = data.email || '';
    document.getElementById('salesRep').value = data.salesRep || '';
    document.getElementById('salesRepPhone').value = data.salesRepPhone || '';
    document.getElementById('retailWebsite').value = data.retailWebsite || '';
    document.getElementById('wholesaleWebsite').value = data.wholesaleWebsite || '';
    document.getElementById('username').value = data.username || '';
    document.getElementById('password').value = data.password || '';
}

function saveSupplier() {
    const data = getFormData();
    
    if (!data.name) {
        alert('Please fill in required field: Supplier Name');
        return;
    }

    if (selectedSupplierIndex >= 0) {
        // Update existing supplier
        suppliers[selectedSupplierIndex] = {
            supplierName: data.name,
            telephone: data.telephone,
            fax: data.fax,
            email: data.email,
            salesRep: data.salesRep,
            repPhone: data.salesRepPhone
        };
    } else {
        // Check if supplier already exists
        const existingIndex = suppliers.findIndex(sup => sup.supplierName === data.name);
        
        if (existingIndex >= 0) {
            // Update existing supplier
            suppliers[existingIndex] = {
                supplierName: data.name,
                telephone: data.telephone,
                fax: data.fax,
                email: data.email,
                salesRep: data.salesRep,
                repPhone: data.salesRepPhone
            };
        } else {
            // Add new supplier
            suppliers.push({
                supplierName: data.name,
                telephone: data.telephone,
                fax: data.fax,
                email: data.email,
                salesRep: data.salesRep,
                repPhone: data.salesRepPhone
            });
        }
    }

    renderSupplierTable();
    console.log('Supplier saved:', data);
    alert('Supplier saved successfully!');
}

function clearForm() {
    setFormData({});
    selectedSupplierIndex = -1;
    
    // Clear table selection
    const rows = document.querySelectorAll('#supplierTableBody tr');
    rows.forEach(row => row.classList.remove('selected'));
    
    console.log('Form cleared');
}

function deleteSupplier() {
    const supplierName = document.getElementById('name').value;
    
    if (!supplierName) {
        alert('Please select a supplier to delete');
        return;
    }

    if (confirm('Are you sure you want to delete this supplier?')) {
        suppliers = suppliers.filter(sup => sup.supplierName !== supplierName);
        renderSupplierTable();
        clearForm();
        console.log('Supplier deleted:', supplierName);
    }
}

function selectSupplier(index) {
    selectedSupplierIndex = index;
    const supplier = suppliers[index];
    
    if (supplier) {
        setFormData({
            name: supplier.supplierName,
            telephone: supplier.telephone,
            fax: supplier.fax,
            email: supplier.email,
            salesRep: supplier.salesRep,
            salesRepPhone: supplier.repPhone
        });
        
        // Update table selection
        const rows = document.querySelectorAll('#supplierTableBody tr');
        rows.forEach((row, i) => {
            if (i === index) {
                row.classList.add('selected');
            } else {
                row.classList.remove('selected');
            }
        });
    }
}

function renderSupplierTable() {
    const tbody = document.getElementById('supplierTableBody');
    tbody.innerHTML = '';

    suppliers.forEach((supplier, index) => {
        const row = document.createElement('tr');
        row.onclick = () => selectSupplier(index);
        
        row.innerHTML = `
            <td class="font-medium">${supplier.supplierName}</td>
            <td class="text-center">
                <input type="tel" class="form-input" value="${supplier.telephone}" 
                       onchange="updateSupplierField(${index}, 'telephone', this.value)">
            </td>
            <td class="text-center">
                <input type="tel" class="form-input" value="${supplier.fax}" 
                       onchange="updateSupplierField(${index}, 'fax', this.value)">
            </td>
            <td class="text-center">
                <input type="email" class="form-input" value="${supplier.email}" 
                       onchange="updateSupplierField(${index}, 'email', this.value)">
            </td>
            <td class="text-center">
                <input type="text" class="form-input" value="${supplier.salesRep}" 
                       onchange="updateSupplierField(${index}, 'salesRep', this.value)">
            </td>
            <td class="text-center">
                <input type="tel" class="form-input" value="${supplier.repPhone}" 
                       onchange="updateSupplierField(${index}, 'repPhone', this.value)">
            </td>
        `;
        
        tbody.appendChild(row);
    });
}

function updateSupplierField(index, field, value) {
    if (suppliers[index]) {
        suppliers[index][field] = value;
    }
}
