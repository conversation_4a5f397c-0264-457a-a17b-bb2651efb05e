<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS System - Rainbow Station Inc</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: #000000;
            color: #00ff00;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            height: 100vh;
            overflow: hidden;
        }

        .pos-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            padding: 8px;
        }

        /* Header */
        .header {
            background-color: #000000;
            border: 1px solid #666666;
            padding: 16px;
            margin-bottom: 8px;
            flex-shrink: 0;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title h1 {
            font-size: 32px;
            font-weight: 900;
            color: #00ff00;
            margin-bottom: 4px;
        }

        .header-title p {
            font-size: 20px;
            font-weight: bold;
            color: #00cc00;
        }

        .header-info {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .time-info {
            text-align: right;
        }

        .time-info div {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .time-info span {
            background-color: #333333;
            padding: 8px 12px;
            font-size: 20px;
            font-weight: 900;
            border-radius: 4px;
        }

        .header-buttons {
            display: flex;
            gap: 8px;
        }

        .btn {
            background-color: #000000;
            color: #ff0000;
            border: 1px solid #00ff00;
            font-size: 20px;
            font-weight: 900;
            padding: 12px 24px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 4px;
        }

        .btn:hover {
            background-color: #333333;
        }

        /* Main Content */
        .main-content {
            display: flex;
            gap: 8px;
            flex: 1;
            overflow: hidden;
        }

        .left-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: calc(100vw - 400px);
        }

        /* Transaction Table */
        .transaction-table {
            background-color: #000000;
            border: 1px solid #666666;
            margin-bottom: 8px;
            flex: 1;
            overflow-y: auto;
        }

        .transaction-table table {
            width: 100%;
            border-collapse: collapse;
        }

        .transaction-table thead {
            position: sticky;
            top: 0;
            background-color: #000000;
        }

        .transaction-table th {
            color: #ff0000;
            padding: 12px;
            text-align: left;
            font-size: 20px;
            font-weight: 900;
            border-bottom: 1px solid #666666;
        }

        .transaction-table td {
            padding: 12px;
            color: #00ff00;
            font-size: 20px;
            font-weight: bold;
            border-bottom: 1px solid #333333;
        }

        .transaction-table tr {
            cursor: pointer;
        }

        .transaction-table tr:hover {
            background-color: #333333;
        }

        .transaction-table tr.selected {
            background-color: #333333;
        }

        .empty-cart {
            padding: 32px;
            text-align: center;
            color: #666666;
            font-size: 20px;
        }

        /* Bottom Sections */
        .bottom-sections {
            margin-top: auto;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .discount-totals {
            display: flex;
            gap: 24px;
        }

        .discount-section, .totals-section {
            flex: 1;
            background-color: #1a1a1a;
            border: 1px solid #666666;
            border-radius: 8px;
            padding: 16px;
        }

        .discount-btn {
            background-color: #000000;
            color: #ff0000;
            border: 1px solid #ff0000;
            font-size: 18px;
            font-weight: 900;
            padding: 12px 24px;
            width: 100%;
            margin-bottom: 12px;
            cursor: pointer;
            border-radius: 4px;
        }

        .checkbox-container {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-top: 8px;
        }

        .checkbox {
            width: 20px;
            height: 20px;
            border: 1px solid #00ff00;
        }

        .totals-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
            margin-bottom: 8px;
        }

        .totals-row.border-top {
            border-top: 1px solid #666666;
            padding-top: 8px;
        }

        .totals-label {
            color: #00ff00;
            font-size: 16px;
            font-weight: bold;
        }

        .totals-value {
            color: #00ff00;
            font-size: 16px;
            font-weight: 900;
            background-color: #333333;
            padding: 4px 12px;
            border-radius: 4px;
        }

        /* Payment Section */
        .payment-section {
            display: flex;
            gap: 12px;
        }

        .item-count {
            background-color: #000000;
            border: 2px solid #666666;
            padding: 24px;
            text-align: center;
            min-width: 100px;
            border-radius: 8px;
        }

        .item-count-number {
            font-size: 72px;
            font-weight: 900;
            color: #00ff00;
        }

        .item-count-label {
            font-size: 14px;
            color: #00cc00;
            margin-top: 4px;
        }

        .cash-change {
            background-color: #000000;
            border: 2px solid #666666;
            padding: 24px;
            flex: 1;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 16px;
        }

        .cash-change-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .cash-change-label {
            color: #00ff00;
            font-size: 24px;
            font-weight: bold;
        }

        .cash-change-value {
            font-size: 40px;
            font-weight: 900;
            color: #00ff00;
            background-color: #333333;
            padding: 12px 24px;
            border-radius: 8px;
            min-width: 140px;
            text-align: center;
        }

        .total-amount {
            background-color: #000000;
            border: 2px solid #666666;
            padding: 24px;
            text-align: center;
            min-width: 220px;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .total-label {
            font-size: 18px;
            color: #00cc00;
            margin-bottom: 12px;
            font-weight: bold;
        }

        .total-value {
            font-size: 80px;
            font-weight: 900;
            color: #00ff00;
            line-height: 1;
        }

        .checkout-button {
            background-color: #000000;
            border: 2px solid #666666;
            padding: 24px;
            min-width: 140px;
            border-radius: 8px;
        }

        .checkout-btn {
            background-color: #000000;
            color: #ff0000;
            border: 2px solid #ff0000;
            width: 100%;
            height: 100%;
            font-size: 24px;
            font-weight: 900;
            cursor: pointer;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .checkout-btn:hover {
            background-color: #330000;
        }

        .checkout-emoji {
            font-size: 32px;
        }

        /* Right Panel */
        .right-panel {
            width: 384px;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        /* Display */
        .display {
            background-color: #000000;
            border: 1px solid #666666;
            padding: 12px;
            margin-bottom: 4px;
            height: 56px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .display-text {
            font-size: 24px;
            font-weight: 900;
            color: #00ff00;
            text-align: center;
        }

        .price-indicator {
            color: #ff0000;
            font-size: 14px;
            margin-left: 8px;
        }

        /* Keypad */
        .keypad {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 4px;
            margin-bottom: 8px;
        }

        .keypad-btn {
            background-color: #000000;
            border: 2px solid #00ff00;
            color: #00ff00;
            font-size: 32px;
            font-weight: 900;
            height: 56px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .keypad-btn:hover {
            background-color: #1a1a1a;
        }

        /* Control Buttons */
        .control-buttons {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 4px;
            margin-bottom: 8px;
        }

        .control-btn {
            background-color: #000000;
            border: 2px solid #00ff00;
            color: #00ff00;
            font-size: 20px;
            font-weight: 900;
            height: 56px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background-color: #1a1a1a;
        }

        .return-btn {
            background-color: #000000;
            color: #ff0000;
            border: 2px solid #ff0000;
            font-size: 20px;
            font-weight: 900;
            height: 56px;
            width: 100%;
            margin-bottom: 8px;
            cursor: pointer;
        }

        /* Function Buttons */
        .function-buttons {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 4px;
            flex: 1;
        }

        .function-btn {
            background-color: #000000;
            border: 1px solid;
            font-size: 18px;
            font-weight: 900;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all 0.3s ease;
        }

        .function-btn.red {
            color: #ff0000;
            border-color: #ff0000;
        }

        .function-btn.blue {
            color: #0088ff;
            border-color: #0088ff;
        }

        .function-btn:hover {
            background-color: #1a1a1a;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            z-index: 1000;
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background-color: #000000;
            border: 2px solid #00ff00;
            color: #00ff00;
            width: 98vw;
            height: 95vh;
            max-width: 1400px;
            max-height: 95vh;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            position: relative;
            margin: auto;
        }

        .modal-header {
            flex-shrink: 0;
            padding: 24px;
            border-bottom: 2px solid #666666;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 40px;
            font-weight: 900;
            color: #00ff00;
        }

        .close-btn {
            background-color: #000000;
            color: #ff0000;
            border: 2px solid #ff0000;
            padding: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background-color: #1a1a1a;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .main-content {
                flex-direction: column;
            }
            
            .right-panel {
                width: 100%;
                order: -1;
                flex: 0 0 auto;
            }
            
            .left-panel {
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="pos-container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <div class="header-title">
                    <h1>POINT OF SALE SYSTEM</h1>
                    <p>Rainbow Station Inc.</p>
                </div>
                <div class="header-info">
                    <div class="time-info">
                        <div>Date: <span id="current-date">01/01/24</span></div>
                        <div>Time: <span id="current-time">00:00:00</span></div>
                        <div>Operator: <span id="current-operator">Loading...</span></div>
                    </div>
                    <div class="header-buttons">
                        <button class="btn" onclick="navigateToAdmin()">ADMIN</button>
                        <button class="btn" onclick="navigateToTheater()">THEATER</button>
                        <button class="btn" onclick="logout()">LOGOUT</button>
                        <button class="btn" onclick="toggleFullscreen()" id="fullscreen-btn" title="Maximize Window">⛶</button>
                        <button class="btn" onclick="minimizeApp()">−</button>
                        <button class="btn" onclick="closeApp()">×</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="main-content">
            <!-- Left Panel -->
            <div class="left-panel">
                <!-- Transaction Table -->
                <div class="transaction-table">
                    <table>
                        <thead>
                            <tr>
                                <th>QH</th>
                                <th>Description</th>
                                <th>Quantity</th>
                                <th>Price</th>
                                <th>Disc</th>
                                <th>Value</th>
                            </tr>
                        </thead>
                        <tbody id="cart-items">
                            <tr>
                                <td colspan="6" class="empty-cart">
                                    No items in cart. Click SELECT ITEM to add products.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Bottom Sections -->
                <div class="bottom-sections">
                    <!-- Discount and Totals -->
                    <div class="discount-totals">
                        <div class="discount-section">
                            <button class="discount-btn">Add Discount</button>
                            <div class="checkbox-container">
                                <input type="checkbox" class="checkbox" id="special-discount">
                                <span class="totals-label">Special Discount</span>
                            </div>
                        </div>
                        <div class="totals-section">
                            <div class="totals-row">
                                <span class="totals-label">Total Item Discount</span>
                                <span class="totals-value" id="item-discount">0.00</span>
                            </div>
                            <div class="totals-row">
                                <span class="totals-label">Discount</span>
                                <span class="totals-value" id="discount-amount">0.00</span>
                            </div>
                            <div class="totals-row border-top">
                                <span class="totals-label">Sub total</span>
                                <span class="totals-value" id="subtotal">0.00</span>
                            </div>
                            <div class="totals-row">
                                <span class="totals-label">Tax 6.625%</span>
                                <span class="totals-value" id="tax-amount">0.00</span>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Section -->
                    <div class="payment-section">
                        <div class="item-count">
                            <div class="item-count-number" id="item-count">0</div>
                            <div class="item-count-label">ITEMS</div>
                        </div>
                        <div class="cash-change">
                            <div class="cash-change-row">
                                <span class="cash-change-label">Cash</span>
                                <div class="cash-change-value" id="cash-amount">0.00</div>
                            </div>
                            <div class="cash-change-row">
                                <span class="cash-change-label">Change</span>
                                <div class="cash-change-value" id="change-amount">0.00</div>
                            </div>
                        </div>
                        <div class="total-amount">
                            <div class="total-label">TOTAL</div>
                            <div class="total-value" id="total-amount">0.00</div>
                        </div>
                        <div class="checkout-button">
                            <button class="checkout-btn" onclick="handleCheckout()">
                                <span>CHECK OUT</span>
                                <span class="checkout-emoji">🛒</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Panel -->
            <div class="right-panel">
                <!-- Display -->
                <div class="display">
                    <div class="display-text">
                        <span id="current-input">0</span>
                        <span class="price-indicator" id="price-indicator" style="display: none;">(PRICE)</span>
                    </div>
                </div>

                <!-- Keypad -->
                <div class="keypad">
                    <button class="keypad-btn" onclick="handleNumberClick('1')">1</button>
                    <button class="keypad-btn" onclick="handleNumberClick('2')">2</button>
                    <button class="keypad-btn" onclick="handleNumberClick('3')">3</button>
                    <button class="keypad-btn" onclick="handleNumberClick('4')">4</button>
                    <button class="keypad-btn" onclick="handleNumberClick('5')">5</button>
                    <button class="keypad-btn" onclick="handleNumberClick('6')">6</button>
                    <button class="keypad-btn" onclick="handleNumberClick('7')">7</button>
                    <button class="keypad-btn" onclick="handleNumberClick('8')">8</button>
                    <button class="keypad-btn" onclick="handleNumberClick('9')">9</button>
                    <button class="keypad-btn" onclick="handleNumberClick('00')">00</button>
                    <button class="keypad-btn" onclick="handleNumberClick('0')">0</button>
                    <button class="keypad-btn" onclick="handleBackspace()">←</button>
                </div>

                <!-- Control Buttons -->
                <div class="control-buttons">
                    <button class="control-btn" onclick="handleClear()">CLEAR</button>
                    <button class="control-btn" onclick="handleEnter()">ENTER</button>
                </div>

                <button class="return-btn">RETURN/EXCHANGE</button>

                <!-- Function Buttons -->
                <div class="function-buttons">
                    <button class="function-btn red" onclick="showProductModal()">
                        <span>SELECT</span>
                        <span>ITEM</span>
                    </button>
                    <button class="function-btn blue">HOLD</button>
                    <button class="function-btn blue">RECALL</button>
                    <button class="function-btn red" onclick="handleEditPrice()">
                        <span>EDIT</span>
                        <span>PRICE</span>
                    </button>
                    <button class="function-btn red">
                        <span>OPEN</span>
                        <span>DRAWER</span>
                    </button>
                    <button class="function-btn red" onclick="handleRemoveItem()">
                        <span>❌</span>
                        <span>CANCEL</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="pos.js"></script>
</body>
</html>
