const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// Sample data
let categories = [
    {
        code: "A001",
        name: "<PERSON><PERSON>",
        parent: "-",
        productsCount: 5,
        status: "active"
    },
    {
        code: "A002",
        name: "<PERSON> Pipes",
        parent: "<PERSON><PERSON>",
        productsCount: 2,
        status: "active"
    },
    {
        code: "A003",
        name: "<PERSON><PERSON> <PERSON>i",
        parent: "-",
        productsCount: 5,
        status: "active"
    },
    {
        code: "B001",
        name: "B. Nailpolish Remo",
        parent: "-",
        productsCount: 5,
        status: "active"
    }
];

let hierarchyData = [
    {
        name: "<PERSON><PERSON>",
        children: ["Water Pipes", "Dry Pipes", "Metal Pipes"]
    },
    {
        name: "<PERSON><PERSON> Deli",
        children: ["Sandwiches", "Salads", "Hot Items"]
    },
    {
        name: "<PERSON><PERSON>",
        children: ["Vitamins", "Pain Relief", "Supplements"]
    },
    {
        name: "B. Nailpolish Remo",
        children: ["Remo<PERSON>", "Pads", "Cuticle Care"]
    }
];

// Initialize the application
document.addEventListener('DOMContentLoaded', async function() {
    updateTime();
    setInterval(updateTime, 1000);
    renderCategoryHierarchy();
    renderCategoryTable();

    // Initialize maximize button state
    try {
        const isMaximized = await ipcRenderer.invoke('is-fullscreen');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = isMaximized ? '🗗' : '⛶';
            fullscreenBtn.title = isMaximized ? 'Restore Window' : 'Maximize Window';
        }
    } catch (error) {
        console.error('Error checking initial maximize state:', error);
    }
});

// Time functions
function updateTime() {
    const now = new Date();
    document.getElementById('current-date').textContent = formatDate(now);
    document.getElementById('current-time').textContent = formatTime(now);
}

function formatDate(date) {
    return date.toLocaleDateString("en-US", { month: "2-digit", day: "2-digit", year: "2-digit" });
}

function formatTime(date) {
    return date.toLocaleTimeString("en-US", { hour12: false });
}

// Navigation functions
function navigateToAdmin() {
    ipcRenderer.invoke('navigate-to', 'admin');
}

// Window control functions
function closeApp() {
    ipcRenderer.invoke('close-app');
}

function minimizeApp() {
    ipcRenderer.invoke('minimize-app');
}

async function toggleFullscreen() {
    try {
        const isMaximized = await ipcRenderer.invoke('toggle-fullscreen');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = isMaximized ? '🗗' : '⛶';
            fullscreenBtn.title = isMaximized ? 'Restore Window' : 'Maximize Window';
        }
    } catch (error) {
        console.error('Error toggling maximize:', error);
    }
}

// Category functions
function addCategory() {
    const categoryName = document.getElementById('categoryName').value;
    const categoryCode = document.getElementById('categoryCode').value;
    const parentCategory = document.getElementById('parentCategory').value;
    const categoryDescription = document.getElementById('categoryDescription').value;
    const displayOrder = document.getElementById('displayOrder').value;
    const categoryStatus = document.getElementById('categoryStatus').value;

    if (!categoryName || !categoryCode) {
        alert('Please fill in required fields: Category Name and Category Code');
        return;
    }

    // Check if code already exists
    if (categories.find(cat => cat.code === categoryCode)) {
        alert('Category code already exists');
        return;
    }

    const newCategory = {
        code: categoryCode,
        name: categoryName,
        parent: parentCategory || "-",
        productsCount: 0,
        status: categoryStatus,
        description: categoryDescription,
        displayOrder: displayOrder
    };

    categories.push(newCategory);
    renderCategoryTable();
    clearForm();
    
    console.log('Category added:', newCategory);
}

function clearForm() {
    document.getElementById('categoryName').value = '';
    document.getElementById('categoryCode').value = '';
    document.getElementById('parentCategory').value = '';
    document.getElementById('categoryDescription').value = '';
    document.getElementById('displayOrder').value = '';
    document.getElementById('categoryStatus').value = 'active';
}

function editCategory(code) {
    const category = categories.find(cat => cat.code === code);
    if (category) {
        document.getElementById('categoryName').value = category.name;
        document.getElementById('categoryCode').value = category.code;
        document.getElementById('parentCategory').value = category.parent === "-" ? "" : category.parent;
        document.getElementById('categoryDescription').value = category.description || '';
        document.getElementById('displayOrder').value = category.displayOrder || '';
        document.getElementById('categoryStatus').value = category.status;
    }
}

function deleteCategory(code) {
    if (confirm('Are you sure you want to delete this category?')) {
        categories = categories.filter(cat => cat.code !== code);
        renderCategoryTable();
        console.log('Category deleted:', code);
    }
}

function importCategories() {
    console.log('Import categories functionality');
    alert('Import categories functionality will be implemented');
}

function exportCategories() {
    console.log('Export categories functionality');
    alert('Export categories functionality will be implemented');
}

function renderCategoryHierarchy() {
    const container = document.getElementById('categoryHierarchy');
    container.innerHTML = '';

    hierarchyData.forEach(item => {
        const hierarchyItem = document.createElement('div');
        hierarchyItem.className = 'hierarchy-item';
        
        hierarchyItem.innerHTML = `
            <div class="hierarchy-title">${item.name}</div>
            <div class="hierarchy-children">
                ${item.children.map(child => `<div class="hierarchy-child">• ${child}</div>`).join('')}
            </div>
        `;
        
        container.appendChild(hierarchyItem);
    });
}

function renderCategoryTable() {
    const tbody = document.getElementById('categoryTableBody');
    tbody.innerHTML = '';

    categories.forEach(category => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td style="color: #2563eb; font-weight: 500;">${category.code}</td>
            <td style="color: #1f2937;">${category.name}</td>
            <td style="color: #6b7280;">${category.parent}</td>
            <td style="color: #6b7280;">${category.productsCount} products</td>
            <td>
                <span class="status-badge status-active">
                    ${category.status.charAt(0).toUpperCase() + category.status.slice(1)}
                </span>
            </td>
            <td>
                <div class="flex gap-2">
                    <button class="btn btn-blue btn-small" onclick="editCategory('${category.code}')">
                        Edit
                    </button>
                    <button class="btn btn-red btn-small" onclick="deleteCategory('${category.code}')">
                        Delete
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}
