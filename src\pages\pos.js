const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// Global state
let cartItems = [];
let currentInput = "";
let selectedItemIndex = 0;
let specialDiscount = false;
let discountAmount = 0;
let isEditingPrice = false;

// Demo products data
const demoProducts = [
    // A. Pi<PERSON>
    { id: "1", name: "Glass Water Pipe", price: 25.99, category: "A. Pipes", subcategory: "Water Pipes", image: "https://via.placeholder.com/200x200/4CAF50/FFFFFF?text=Glass+Pipe" },
    { id: "2", name: "Ceramic Smoking Pipe", price: 15.50, category: "A. Pipes", subcategory: "Dry Pipes", image: "https://via.placeholder.com/200x200/2196F3/FFFFFF?text=Ceramic+Pipe" },
    { id: "3", name: "Metal Hand Pipe", price: 12.99, category: "A. Pipes", subcategory: "Metal Pipes", image: "https://via.placeholder.com/200x200/FF9800/FFFFFF?text=Metal+Pipe" },
    { id: "4", name: "Silicone Bong", price: 35.00, category: "A. Pipes", subcategory: "Water Pipes", image: "https://via.placeholder.com/200x200/9C27B0/FFFFFF?text=Silicone+Bong" },
    { id: "5", name: "Wooden Pipe", price: 18.75, category: "A. Pipes", subcategory: "Dry Pipes", image: "https://via.placeholder.com/200x200/795548/FFFFFF?text=Wood+Pipe" },

    // A. Deli
    { id: "6", name: "Turkey Sandwich", price: 8.99, category: "A. Deli", subcategory: "Sandwiches", image: "https://via.placeholder.com/200x200/FF5722/FFFFFF?text=Turkey+Sandwich" },
    { id: "7", name: "Ham & Cheese", price: 7.50, category: "A. Deli", subcategory: "Sandwiches", image: "https://via.placeholder.com/200x200/FFC107/FFFFFF?text=Ham+Cheese" },
    { id: "8", name: "Chicken Salad", price: 6.25, category: "A. Deli", subcategory: "Salads", image: "https://via.placeholder.com/200x200/8BC34A/FFFFFF?text=Chicken+Salad" },
    { id: "9", name: "Tuna Melt", price: 9.25, category: "A. Deli", subcategory: "Hot Items", image: "https://via.placeholder.com/200x200/CDDC39/FFFFFF?text=Tuna+Melt" },
    { id: "10", name: "Caesar Salad", price: 7.99, category: "A. Deli", subcategory: "Salads", image: "https://via.placeholder.com/200x200/4CAF50/FFFFFF?text=Caesar+Salad" },

    // A. Pills
    { id: "11", name: "Vitamin C 1000mg", price: 12.99, category: "A. Pills", subcategory: "Vitamins", image: "https://via.placeholder.com/200x200/FF9800/FFFFFF?text=Vitamin+C" },
    { id: "12", name: "Multivitamin", price: 15.50, category: "A. Pills", subcategory: "Vitamins", image: "https://via.placeholder.com/200x200/E91E63/FFFFFF?text=Multivitamin" },
    { id: "13", name: "Aspirin 325mg", price: 8.99, category: "A. Pills", subcategory: "Pain Relief", image: "https://via.placeholder.com/200x200/F44336/FFFFFF?text=Aspirin" },
    { id: "14", name: "Ibuprofen 200mg", price: 9.75, category: "A. Pills", subcategory: "Pain Relief", image: "https://via.placeholder.com/200x200/2196F3/FFFFFF?text=Ibuprofen" },
    { id: "15", name: "Calcium Supplement", price: 11.25, category: "A. Pills", subcategory: "Supplements", image: "https://via.placeholder.com/200x200/607D8B/FFFFFF?text=Calcium" },

    // B. Nailpolish Remo
    { id: "16", name: "Acetone Remover", price: 4.99, category: "B. Nailpolish Remo", subcategory: "Removers", image: "https://via.placeholder.com/200x200/E91E63/FFFFFF?text=Acetone" },
    { id: "17", name: "Non-Acetone Remover", price: 5.50, category: "B. Nailpolish Remo", subcategory: "Removers", image: "https://via.placeholder.com/200x200/9C27B0/FFFFFF?text=Non-Acetone" },
    { id: "18", name: "Nail Polish Remover Pads", price: 6.25, category: "B. Nailpolish Remo", subcategory: "Pads", image: "https://via.placeholder.com/200x200/673AB7/FFFFFF?text=Remover+Pads" },
    { id: "19", name: "Cuticle Remover", price: 7.99, category: "B. Nailpolish Remo", subcategory: "Cuticle Care", image: "https://via.placeholder.com/200x200/3F51B5/FFFFFF?text=Cuticle+Oil" },
    { id: "20", name: "Gel Polish Remover", price: 8.75, category: "B. Nailpolish Remo", subcategory: "Gel Removers", image: "https://via.placeholder.com/200x200/009688/FFFFFF?text=Gel+Remover" },

    // Black Unicorn
    { id: "21", name: "Black Unicorn Energy Drink", price: 3.99, category: "Black Unicorn", subcategory: "Energy Drinks", image: "https://via.placeholder.com/200x200/212121/FFFFFF?text=Energy+Drink" },
    { id: "22", name: "Black Unicorn Protein Bar", price: 4.50, category: "Black Unicorn", subcategory: "Protein", image: "https://via.placeholder.com/200x200/424242/FFFFFF?text=Protein+Bar" },
    { id: "23", name: "Black Unicorn Pre-Workout", price: 29.99, category: "Black Unicorn", subcategory: "Supplements", image: "https://via.placeholder.com/200x200/616161/FFFFFF?text=Pre-Workout" },
    { id: "24", name: "Black Unicorn Recovery Drink", price: 5.25, category: "Black Unicorn", subcategory: "Recovery", image: "https://via.placeholder.com/200x200/757575/FFFFFF?text=Recovery" },
    { id: "25", name: "Black Unicorn BCAA", price: 24.99, category: "Black Unicorn", subcategory: "Supplements", image: "https://via.placeholder.com/200x200/9E9E9E/FFFFFF?text=BCAA" },

    // C. Miscellaneous
    { id: "26", name: "Phone Charger", price: 12.99, category: "C. Miscellaneous", subcategory: "Electronics", image: "https://via.placeholder.com/200x200/607D8B/FFFFFF?text=Phone+Charger" },
    { id: "27", name: "Lighter", price: 1.99, category: "C. Miscellaneous", subcategory: "Accessories", image: "https://via.placeholder.com/200x200/FF5722/FFFFFF?text=Lighter" },
    { id: "28", name: "Sunglasses", price: 15.99, category: "C. Miscellaneous", subcategory: "Accessories", image: "https://via.placeholder.com/200x200/795548/FFFFFF?text=Sunglasses" },
    { id: "29", name: "Keychain", price: 3.50, category: "C. Miscellaneous", subcategory: "Accessories", image: "https://via.placeholder.com/200x200/9E9E9E/FFFFFF?text=Keychain" },
    { id: "30", name: "Notebook", price: 4.99, category: "C. Miscellaneous", subcategory: "Stationery", image: "https://via.placeholder.com/200x200/CDDC39/FFFFFF?text=Notebook" },
    { id: "31", name: "Pen Set", price: 7.25, category: "C. Miscellaneous", subcategory: "Stationery", image: "https://via.placeholder.com/200x200/FFC107/FFFFFF?text=Pen+Set" },
    { id: "32", name: "USB Cable", price: 8.99, category: "C. Miscellaneous", subcategory: "Electronics", image: "https://via.placeholder.com/200x200/3F51B5/FFFFFF?text=USB+Cable" },
];

// Current user management
let currentUser = null;
let userPermissions = [];

async function loadCurrentUser() {
    try {
        console.log('POS - Loading current user...');
        currentUser = await ipcRenderer.invoke('get-current-user');

        if (currentUser) {
            // Extract permissions safely
            userPermissions = currentUser.permissions || [];

            console.log('POS - User loaded successfully:', {
                username: currentUser.username,
                role: currentUser.role,
                permissionCount: userPermissions.length
            });

            // Log permissions for debugging
            if (userPermissions.length > 0) {
                console.log('POS - User permissions:', userPermissions.map(p => p.module_id));
            }

            updateOperatorInfo();
        } else {
            console.error('POS - No current user returned - this should not happen');
            // DO NOT set fallback admin - this compromises security
            currentUser = null;
            userPermissions = [];
            document.getElementById('current-operator').textContent = 'Error: No User';
        }
    } catch (error) {
        console.error('Error loading current user in POS:', error);
        // DO NOT set fallback admin - this compromises security
        currentUser = null;
        userPermissions = [];
        document.getElementById('current-operator').textContent = 'Error: User Load Failed';
    }
}

function updateOperatorInfo() {
    if (currentUser) {
        const operatorSpan = document.getElementById('current-operator');
        if (operatorSpan) {
            operatorSpan.textContent = currentUser.name || currentUser.username || 'Unknown';
        }
    }
}

// Permission checking functions - RESTRICTIVE (deny by default)
function hasModuleAccess(moduleId) {
    try {
        // If no current user loaded, DENY access
        if (!currentUser) {
            console.log('POS - No current user loaded, DENYING access to', moduleId);
            return false;
        }

        // Admin has access to everything
        if (currentUser.role === 'Admin') {
            console.log('POS - Admin user, allowing access to', moduleId);
            return true;
        }

        // Check if user has permission for this module
        const hasAccess = userPermissions && userPermissions.some(perm => perm.module_id === moduleId);
        console.log(`POS - Access check for ${moduleId}: ${hasAccess} (user: ${currentUser.username}, role: ${currentUser.role})`);

        return hasAccess;
    } catch (error) {
        console.error('Error checking module access:', error);
        // Default to DENYING access if there's an error
        return false;
    }
}

function canAccessAdmin() {
    return hasModuleAccess('dashboard') || hasModuleAccess('master') || hasModuleAccess('reports') ||
           hasModuleAccess('transactions') || hasModuleAccess('wholesale') || hasModuleAccess('user-management');
}

function canAccessTheater() {
    return hasModuleAccess('theater');
}

// Apply access control restrictions to POS interface
function applyAccessControlRestrictions() {
    console.log('POS - Applying access control restrictions...');

    if (!currentUser) {
        console.warn('POS - No current user, cannot apply restrictions');
        return;
    }

    restrictAdminButton();
    restrictTheaterButton();

    console.log('POS - Access control restrictions applied');
}

function restrictAdminButton() {
    const adminBtn = document.querySelector('button[onclick="navigateToAdmin()"]');

    if (adminBtn) {
        if (!canAccessAdmin()) {
            console.log('POS - Restricting admin button access');
            adminBtn.style.opacity = '0.5';
            adminBtn.style.cursor = 'not-allowed';
            adminBtn.style.pointerEvents = 'none';
            adminBtn.title = 'Access Denied: You do not have permission to access Admin Panel';

            // Replace onclick with access denied message
            adminBtn.removeAttribute('onclick');
            adminBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                showAccessDeniedMessage('Admin Panel');
            });
        } else {
            console.log('POS - Admin button access allowed');
        }
    }
}

function restrictTheaterButton() {
    const theaterBtn = document.querySelector('button[onclick="navigateToTheater()"]');

    if (theaterBtn) {
        if (!canAccessTheater()) {
            console.log('POS - Restricting theater button access');
            theaterBtn.style.opacity = '0.5';
            theaterBtn.style.cursor = 'not-allowed';
            theaterBtn.style.pointerEvents = 'none';
            theaterBtn.title = 'Access Denied: You do not have permission to access Theater Management';

            // Replace onclick with access denied message
            theaterBtn.removeAttribute('onclick');
            theaterBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                showAccessDeniedMessage('Theater Management');
            });
        } else {
            console.log('POS - Theater button access allowed');
        }
    }
}

function showAccessDeniedMessage(moduleName) {
    alert(`Access Denied: You do not have permission to access ${moduleName}.`);
}

// Initialize the application
document.addEventListener('DOMContentLoaded', async function() {
    console.log('POS - DOM Content Loaded, initializing...');

    updateTime();
    setInterval(updateTime, 1000);
    updateDisplay();
    updateTotals();

    // Load current user information first
    await loadCurrentUser();

    console.log('POS - User loading completed, permissions ready');

    // Apply access control restrictions
    applyAccessControlRestrictions();

    // Initialize maximize button state
    try {
        const isMaximized = await ipcRenderer.invoke('is-fullscreen');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = isMaximized ? '🗗' : '⛶';
            fullscreenBtn.title = isMaximized ? 'Restore Window' : 'Maximize Window';
        }
    } catch (error) {
        console.error('Error checking initial maximize state:', error);
    }

    console.log('POS - Initialization completed successfully');
});

// Time functions
function updateTime() {
    const now = new Date();
    document.getElementById('current-date').textContent = formatDate(now);
    document.getElementById('current-time').textContent = formatTime(now);
}

function formatDate(date) {
    return date.toLocaleDateString("en-US", { month: "2-digit", day: "2-digit", year: "2-digit" });
}

function formatTime(date) {
    return date.toLocaleTimeString("en-US", { hour12: false });
}

function formatCurrency(amount) {
    return amount.toFixed(2);
}

// Navigation functions with permission checks
function navigateToAdmin() {
    if (canAccessAdmin()) {
        console.log('POS - Navigating to admin (permission granted)');
        ipcRenderer.invoke('navigate-to', 'admin');
    } else {
        console.log('POS - Admin navigation blocked (no permission)');
        showAccessDeniedMessage('Admin Panel');
    }
}

function navigateToTheater() {
    if (canAccessTheater()) {
        console.log('POS - Navigating to theater (permission granted)');
        ipcRenderer.invoke('navigate-to', 'theater');
    } else {
        console.log('POS - Theater navigation blocked (no permission)');
        showAccessDeniedMessage('Theater Management');
    }
}

// Logout function
function logout() {
    if (confirm('Are you sure you want to logout?')) {
        // Clear any local data/state if needed
        try {
            ipcRenderer.invoke('logout').then(() => {
                console.log('Logout successful');
            }).catch((error) => {
                console.error('Logout error:', error);
            });
        } catch (error) {
            console.error('Logout error:', error);
        }
    }
}

// Window control functions
function closeApp() {
    ipcRenderer.invoke('close-app');
}

function minimizeApp() {
    ipcRenderer.invoke('minimize-app');
}

async function toggleFullscreen() {
    try {
        console.log('Toggling maximize...');
        const isMaximized = await ipcRenderer.invoke('toggle-fullscreen');
        console.log('Maximized state:', isMaximized);
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = isMaximized ? '🗗' : '⛶';
            fullscreenBtn.title = isMaximized ? 'Restore Window' : 'Maximize Window';
            console.log('Updated button text to:', fullscreenBtn.textContent);
        } else {
            console.error('Maximize button not found');
        }
    } catch (error) {
        console.error('Error toggling maximize:', error);
    }
}

// Keypad functions
function handleNumberClick(num) {
    currentInput += num;
    updateDisplay();
}

function handleBackspace() {
    currentInput = currentInput.slice(0, -1);
    updateDisplay();
}

function handleClear() {
    currentInput = "";
    isEditingPrice = false;
    updateDisplay();
}

function handleEnter() {
    if (currentInput && cartItems.length > 0) {
        if (isEditingPrice) {
            // Update price of selected item
            const newPrice = parseFloat(currentInput);
            if (newPrice > 0 && selectedItemIndex < cartItems.length) {
                cartItems[selectedItemIndex].price = newPrice;
                cartItems[selectedItemIndex].value = cartItems[selectedItemIndex].quantity * newPrice;
                updateCartDisplay();
                updateTotals();
            }
            isEditingPrice = false;
        } else {
            // Update quantity of selected item
            const quantity = parseInt(currentInput);
            if (quantity > 0 && selectedItemIndex < cartItems.length) {
                cartItems[selectedItemIndex].quantity = quantity;
                cartItems[selectedItemIndex].value = quantity * cartItems[selectedItemIndex].price;
                updateCartDisplay();
                updateTotals();
            }
        }
        currentInput = "";
        updateDisplay();
    }
}

function handleEditPrice() {
    if (cartItems.length > 0 && selectedItemIndex < cartItems.length) {
        isEditingPrice = true;
        currentInput = cartItems[selectedItemIndex].price.toString();
        updateDisplay();
    }
}

function handleRemoveItem() {
    if (cartItems.length > 0 && selectedItemIndex < cartItems.length) {
        cartItems.splice(selectedItemIndex, 1);
        if (selectedItemIndex >= cartItems.length) {
            selectedItemIndex = Math.max(0, cartItems.length - 1);
        }
        updateCartDisplay();
        updateTotals();
    }
}

// Display functions
function updateDisplay() {
    const displayText = currentInput || "0";
    document.getElementById('current-input').textContent = displayText;
    
    const priceIndicator = document.getElementById('price-indicator');
    if (isEditingPrice) {
        priceIndicator.style.display = 'inline';
    } else {
        priceIndicator.style.display = 'none';
    }
}

// Cart functions
function addItemToCart(product) {
    const existingItemIndex = cartItems.findIndex(item => item.description === product.name);

    if (existingItemIndex >= 0) {
        // If item already exists, increase quantity
        cartItems[existingItemIndex].quantity += 1;
        cartItems[existingItemIndex].value = cartItems[existingItemIndex].quantity * cartItems[existingItemIndex].price;
    } else {
        // Add new item
        const newItem = {
            id: Date.now().toString(),
            qh: cartItems.length,
            description: product.name,
            quantity: 1,
            price: product.price,
            discount: 0,
            value: product.price,
        };
        cartItems.push(newItem);
    }
    
    updateCartDisplay();
    updateTotals();
}

function removeItemFromCart(product) {
    const existingItemIndex = cartItems.findIndex(item => item.description === product.name);

    if (existingItemIndex >= 0) {
        if (cartItems[existingItemIndex].quantity > 1) {
            // Decrease quantity
            cartItems[existingItemIndex].quantity -= 1;
            cartItems[existingItemIndex].value = cartItems[existingItemIndex].quantity * cartItems[existingItemIndex].price;
        } else {
            // Remove item completely
            cartItems.splice(existingItemIndex, 1);
            if (selectedItemIndex >= cartItems.length) {
                selectedItemIndex = Math.max(0, cartItems.length - 1);
            }
        }
        updateCartDisplay();
        updateTotals();
    }
}

function updateCartDisplay() {
    const tbody = document.getElementById('cart-items');

    if (cartItems.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="empty-cart">
                    No items in cart. Click SELECT ITEM to add products.
                </td>
            </tr>
        `;
    } else {
        tbody.innerHTML = cartItems.map((item, index) => `
            <tr class="${selectedItemIndex === index ? 'selected' : ''}" onclick="selectItem(${index})">
                <td>${index}</td>
                <td>${item.description}</td>
                <td>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <button onclick="event.stopPropagation(); changeQuantity(${index}, -1)"
                                style="background: #aa0000; color: white; border: 1px solid #ff0000; width: 24px; height: 24px; border-radius: 4px; cursor: pointer; font-size: 14px; font-weight: bold;">-</button>
                        <span style="min-width: 30px; text-align: center; font-weight: bold;">${item.quantity}</span>
                        <button onclick="event.stopPropagation(); changeQuantity(${index}, 1)"
                                style="background: #00aa00; color: white; border: 1px solid #00ff00; width: 24px; height: 24px; border-radius: 4px; cursor: pointer; font-size: 14px; font-weight: bold;">+</button>
                    </div>
                </td>
                <td>${formatCurrency(item.price)}</td>
                <td>${formatCurrency(item.discount)}</td>
                <td>${formatCurrency(item.value)}</td>
            </tr>
        `).join('');
    }
}

function selectItem(index) {
    selectedItemIndex = index;
    updateCartDisplay();
}

// Function to change quantity directly from cart
function changeQuantity(index, change) {
    if (index >= 0 && index < cartItems.length) {
        const newQuantity = cartItems[index].quantity + change;

        if (newQuantity > 0) {
            cartItems[index].quantity = newQuantity;
            cartItems[index].value = newQuantity * cartItems[index].price;
            updateCartDisplay();
            updateTotals();
        } else if (newQuantity === 0) {
            // Remove item if quantity becomes 0
            cartItems.splice(index, 1);
            if (selectedItemIndex >= cartItems.length) {
                selectedItemIndex = Math.max(0, cartItems.length - 1);
            }
            updateCartDisplay();
            updateTotals();
        }
    }
}

// Calculation functions
function calculateSubtotal() {
    return cartItems.reduce((sum, item) => sum + item.value, 0);
}

function calculateTax() {
    return calculateSubtotal() * 0.06625;
}

function calculateTotal() {
    return calculateSubtotal() + calculateTax() - discountAmount;
}

function updateTotals() {
    document.getElementById('item-discount').textContent = formatCurrency(0.0);
    document.getElementById('discount-amount').textContent = formatCurrency(discountAmount);
    document.getElementById('subtotal').textContent = formatCurrency(calculateSubtotal());
    document.getElementById('tax-amount').textContent = formatCurrency(calculateTax());
    document.getElementById('item-count').textContent = cartItems.length;
    document.getElementById('cash-amount').textContent = formatCurrency(calculateTotal());
    document.getElementById('change-amount').textContent = formatCurrency(0.00);
    document.getElementById('total-amount').textContent = formatCurrency(calculateTotal());

    // Update modal footer if modal is open
    updateModalFooter();
}

// Checkout function
function handleCheckout() {
    if (cartItems.length > 0) {
        showCheckoutConfirmationModal();
    }
}

// Global variable to store selected payment method
let selectedPaymentMethod = 'Cash';

function showCheckoutConfirmationModal() {
    // Create checkout confirmation modal
    const modal = document.createElement('div');
    modal.className = 'modal show';
    modal.id = 'checkout-confirmation-modal';

    // Generate product list HTML
    const productListHTML = cartItems.map(item => `
        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; border-bottom: 1px solid #333; background-color: #1a1a1a; margin-bottom: 8px; border-radius: 4px;">
            <div style="flex: 1;">
                <div style="font-size: 18px; font-weight: bold; color: #00ff00;">${item.description}</div>
                <div style="font-size: 14px; color: #00cc00;">Unit Price: $${formatCurrency(item.price)}</div>
            </div>
            <div style="text-align: center; margin: 0 20px;">
                <div style="font-size: 16px; color: #00ff00;">Qty: ${item.quantity}</div>
            </div>
            <div style="text-align: right;">
                <div style="font-size: 18px; font-weight: bold; color: #00ff00;">$${formatCurrency(item.value)}</div>
                ${item.discount > 0 ? `<div style="font-size: 14px; color: #ff6600;">Discount: $${formatCurrency(item.discount)}</div>` : ''}
            </div>
        </div>
    `).join('');

    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">CHECKOUT CONFIRMATION</div>
                <button class="close-btn" onclick="closeCheckoutModal()">×</button>
            </div>
            <div style="padding: 24px; display: flex; flex-direction: column; gap: 24px;">
                <!-- Product List Section -->
                <div style="background-color: #0a0a0a; border: 2px solid #00ff00; border-radius: 8px; padding: 20px;">
                    <h3 style="color: #00ff00; font-size: 24px; font-weight: bold; margin-bottom: 16px; text-align: center;">ITEMS TO PURCHASE</h3>
                    <div style="max-height: 300px; overflow-y: auto;">
                        ${productListHTML}
                    </div>
                </div>

                <!-- Payment Method Selection -->
                <div style="background-color: #0a0a0a; border: 2px solid #00ff00; border-radius: 8px; padding: 20px;">
                    <h3 style="color: #00ff00; font-size: 24px; font-weight: bold; margin-bottom: 16px; text-align: center;">PAYMENT METHOD</h3>
                    <div style="display: flex; gap: 16px; justify-content: center;">
                        <button onclick="selectPaymentMethod('Cash')" id="payment-cash" style="background-color: #006600; color: #00ff00; border: 2px solid #00ff00; font-size: 20px; font-weight: bold; padding: 16px 32px; cursor: pointer; border-radius: 8px; transition: all 0.3s ease;">
                            💵 CASH
                        </button>
                        <button onclick="selectPaymentMethod('Debit')" id="payment-debit" style="background-color: #1a1a1a; color: #00ff00; border: 2px solid #666; font-size: 20px; font-weight: bold; padding: 16px 32px; cursor: pointer; border-radius: 8px; transition: all 0.3s ease;">
                            💳 DEBIT
                        </button>
                        <button onclick="selectPaymentMethod('Credit')" id="payment-credit" style="background-color: #1a1a1a; color: #00ff00; border: 2px solid #666; font-size: 20px; font-weight: bold; padding: 16px 32px; cursor: pointer; border-radius: 8px; transition: all 0.3s ease;">
                            💳 CREDIT
                        </button>
                        <button onclick="selectPaymentMethod('Other')" id="payment-other" style="background-color: #1a1a1a; color: #00ff00; border: 2px solid #666; font-size: 20px; font-weight: bold; padding: 16px 32px; cursor: pointer; border-radius: 8px; transition: all 0.3s ease;">
                            🏦 OTHER
                        </button>
                    </div>
                </div>

                <!-- Summary Section -->
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 16px;">
                    <div style="background-color: #1a1a1a; border: 1px solid #00ff00; border-radius: 8px; padding: 20px; text-align: center;">
                        <div style="font-size: 16px; font-weight: bold; color: #00cc00; margin-bottom: 8px;">Items</div>
                        <div style="font-size: 32px; font-weight: 900; color: #00ff00;">${cartItems.length}</div>
                    </div>
                    <div style="background-color: #1a1a1a; border: 1px solid #00ff00; border-radius: 8px; padding: 20px; text-align: center;">
                        <div style="font-size: 16px; font-weight: bold; color: #00cc00; margin-bottom: 8px;">Subtotal</div>
                        <div style="font-size: 32px; font-weight: 900; color: #00ff00;">$${formatCurrency(calculateSubtotal())}</div>
                    </div>
                    <div style="background-color: #1a1a1a; border: 1px solid #00ff00; border-radius: 8px; padding: 20px; text-align: center;">
                        <div style="font-size: 16px; font-weight: bold; color: #00cc00; margin-bottom: 8px;">Tax (6.625%)</div>
                        <div style="font-size: 32px; font-weight: 900; color: #00ff00;">$${formatCurrency(calculateTax())}</div>
                    </div>
                    <div style="background-color: #006600; border: 2px solid #00ff00; border-radius: 8px; padding: 20px; text-align: center;">
                        <div style="font-size: 16px; font-weight: bold; color: #000; margin-bottom: 8px;">TOTAL</div>
                        <div style="font-size: 32px; font-weight: 900; color: #000;">$${formatCurrency(calculateTotal())}</div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div style="display: flex; gap: 24px; justify-content: center; margin-top: 24px;">
                    <button onclick="confirmCheckout()" style="background-color: #00aa00; color: #000000; border: 2px solid #00ff00; font-size: 28px; font-weight: 900; padding: 20px 40px; cursor: pointer; border-radius: 8px; transition: all 0.3s ease;">
                        ✓ CONFIRM CHECKOUT
                    </button>
                    <button onclick="closeCheckoutModal()" style="background-color: #aa0000; color: #ffffff; border: 2px solid #ff0000; font-size: 28px; font-weight: 900; padding: 20px 40px; cursor: pointer; border-radius: 8px; transition: all 0.3s ease;">
                        ✗ CANCEL
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

// Payment method selection function
function selectPaymentMethod(method) {
    selectedPaymentMethod = method;

    // Update button styles
    const buttons = ['payment-cash', 'payment-debit', 'payment-credit', 'payment-other'];
    buttons.forEach(buttonId => {
        const button = document.getElementById(buttonId);
        if (button) {
            if (buttonId === `payment-${method.toLowerCase()}`) {
                button.style.backgroundColor = '#006600';
                button.style.borderColor = '#00ff00';
                button.style.color = '#00ff00';
            } else {
                button.style.backgroundColor = '#1a1a1a';
                button.style.borderColor = '#666';
                button.style.color = '#00ff00';
            }
        }
    });
}

// Confirm checkout and show success modal
function confirmCheckout() {
    closeCheckoutModal();
    showSuccessModal();
}

// Show final success modal
function showSuccessModal() {
    const modal = document.createElement('div');
    modal.className = 'modal show';
    modal.id = 'success-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">SUCCESSFUL SALE!</div>
                <button class="close-btn" onclick="closeCheckoutModal()">×</button>
            </div>
            <div style="text-align: center; padding: 32px; display: flex; flex-direction: column; gap: 32px;">
                <div style="background-color: #1a1a1a; border: 2px solid #00ff00; border-radius: 8px; padding: 32px;">
                    <div style="font-size: 96px; font-weight: 900; color: #00ff00; line-height: 1;">$${formatCurrency(calculateTotal())}</div>
                </div>

                <div style="background-color: #0a0a0a; border: 2px solid #00ff00; border-radius: 8px; padding: 20px; margin: 16px 0;">
                    <div style="font-size: 24px; font-weight: bold; color: #00ff00; margin-bottom: 12px;">Payment Method: ${selectedPaymentMethod}</div>
                    <div style="font-size: 18px; color: #00cc00;">Transaction completed successfully!</div>
                </div>

                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 24px;">
                    <div style="background-color: #1a1a1a; border: 1px solid #00ff00; border-radius: 8px; padding: 24px;">
                        <div style="font-size: 24px; font-weight: bold; color: #00cc00; margin-bottom: 8px;">Items Sold</div>
                        <div style="font-size: 48px; font-weight: 900; color: #00ff00;">${cartItems.length}</div>
                    </div>
                    <div style="background-color: #1a1a1a; border: 1px solid #00ff00; border-radius: 8px; padding: 24px;">
                        <div style="font-size: 24px; font-weight: bold; color: #00cc00; margin-bottom: 8px;">Subtotal</div>
                        <div style="font-size: 48px; font-weight: 900; color: #00ff00;">$${formatCurrency(calculateSubtotal())}</div>
                    </div>
                    <div style="background-color: #1a1a1a; border: 1px solid #00ff00; border-radius: 8px; padding: 24px;">
                        <div style="font-size: 24px; font-weight: bold; color: #00cc00; margin-bottom: 8px;">Tax (6.625%)</div>
                        <div style="font-size: 48px; font-weight: 900; color: #00ff00;">$${formatCurrency(calculateTax())}</div>
                    </div>
                </div>

                <div style="display: flex; gap: 24px; justify-content: center; margin-top: 48px;">
                    <button onclick="completeCheckout()" style="background-color: #00aa00; color: #000000; border: 2px solid #00ff00; font-size: 32px; font-weight: 900; padding: 24px 48px; cursor: pointer; border-radius: 8px;">
                        NEW TRANSACTION
                    </button>
                    <button onclick="closeCheckoutModal()" style="background-color: #aa0000; color: #ffffff; border: 2px solid #ff0000; font-size: 32px; font-weight: 900; padding: 24px 48px; cursor: pointer; border-radius: 8px;">
                        CLOSE
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

function closeCheckoutModal() {
    const modal = document.querySelector('.modal');
    if (modal) {
        modal.remove();
    }
}

function completeCheckout() {
    cartItems = [];
    currentInput = "";
    selectedItemIndex = 0;
    discountAmount = 0;
    specialDiscount = false;
    isEditingPrice = false;
    selectedPaymentMethod = 'Cash'; // Reset payment method

    updateCartDisplay();
    updateTotals();
    updateDisplay();
    closeCheckoutModal();
}

// Product modal functions
let selectedCategory = null;
let selectedSubcategory = null;

function showProductModal() {
    selectedCategory = null;
    selectedSubcategory = null;

    const modal = document.createElement('div');
    modal.className = 'modal show';
    modal.id = 'product-modal';
    modal.innerHTML = `
        <div style="background-color: #ffffff; border: none; border-radius: 16px; color: #333333; width: 95vw; height: 90vh; max-width: 1400px; overflow: hidden; display: flex; flex-direction: column; position: relative; margin: auto; box-shadow: 0 20px 60px rgba(0,0,0,0.3);">
            <!-- Header -->
            <div style="flex-shrink: 0; padding: 24px 32px; border-bottom: 1px solid #e5e7eb; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <h2 style="font-size: 32px; font-weight: 700; margin: 0; color: white;">Select Products</h2>
                    <button onclick="closeProductModal()" style="background: rgba(255,255,255,0.2); color: white; border: none; width: 48px; height: 48px; border-radius: 50%; cursor: pointer; font-size: 24px; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;" onmouseover="this.style.background='rgba(255,255,255,0.3)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'">×</button>
                </div>
            </div>

            <!-- Category Filter Bar -->
            <div style="flex-shrink: 0; padding: 20px 32px; background-color: #f8fafc; border-bottom: 1px solid #e5e7eb;">
                <div style="display: flex; gap: 12px; align-items: center; flex-wrap: wrap;">
                    <span style="font-weight: 600; color: #374151; margin-right: 8px;">Categories:</span>
                    <button onclick="selectCategory(null)" id="category-all" style="padding: 8px 16px; border: 2px solid #667eea; background: #667eea; color: white; border-radius: 25px; cursor: pointer; font-weight: 500; transition: all 0.3s ease;">All Products</button>
                    ${[...new Set(demoProducts.map(p => p.category))].map(category => `
                        <button onclick="selectCategory('${category}')" id="category-${category.replace(/[^a-zA-Z0-9]/g, '')}" style="padding: 8px 16px; border: 2px solid #e5e7eb; background: white; color: #374151; border-radius: 25px; cursor: pointer; font-weight: 500; transition: all 0.3s ease;" onmouseover="this.style.background='#f3f4f6'" onmouseout="this.style.background='white'">${category}</button>
                    `).join('')}
                </div>
            </div>

            <!-- Subcategory Filter Bar -->
            <div id="subcategory-filter-bar" style="flex-shrink: 0; padding: 15px 32px; background-color: #f1f5f9; border-bottom: 1px solid #e5e7eb; display: none;">
                <div style="display: flex; gap: 12px; align-items: center; flex-wrap: wrap;">
                    <span style="font-weight: 600; color: #374151; margin-right: 8px;">Subcategories:</span>
                    <button onclick="selectSubcategory(null)" id="subcategory-all" style="padding: 6px 14px; border: 2px solid #10b981; background: #10b981; color: white; border-radius: 20px; cursor: pointer; font-weight: 500; transition: all 0.3s ease; font-size: 14px;">All Items</button>
                    <div id="subcategories-list" style="display: flex; gap: 8px; flex-wrap: wrap;">
                        <!-- Subcategories will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Products Grid -->
            <div style="flex: 1; padding: 24px 32px; overflow-y: auto; background-color: #ffffff;">
                <div id="products-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 20px;">
                    <!-- Products will be populated here -->
                </div>
                <div id="no-products" style="display: none; text-align: center; padding: 60px 20px; color: #9ca3af;">
                    <div style="font-size: 48px; margin-bottom: 16px;">📦</div>
                    <div style="font-size: 18px; font-weight: 500;">No products found</div>
                    <div style="font-size: 14px; margin-top: 8px;">Try selecting a different category</div>
                </div>
            </div>

            <!-- Footer -->
            <div id="modal-footer" style="flex-shrink: 0; padding: 20px 32px; background-color: #f8fafc; border-top: 1px solid #e5e7eb;">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div style="color: #374151; font-weight: 600; font-size: 16px;">
                        Items in Cart: <span style="color: #667eea; font-size: 20px; font-weight: 700;">${cartItems.length}</span>
                    </div>
                    <div style="color: #374151; font-weight: 600; font-size: 16px;">
                        Total: <span style="color: #10b981; font-size: 24px; font-weight: 700;">$${formatCurrency(calculateTotal())}</span>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    populateProductsGrid();
}

function closeProductModal() {
    const modal = document.getElementById('product-modal');
    if (modal) {
        modal.remove();
    }
}

function populateCategories() {
    const categories = [...new Set(demoProducts.map(p => p.category))];
    const categoriesList = document.getElementById('categories-list');

    categoriesList.innerHTML = categories.map(category => `
        <div onclick="selectCategory('${category}')"
             style="padding: 12px; margin-bottom: 8px; cursor: pointer; border: 2px solid #666666; border-radius: 8px; font-size: 16px; font-weight: bold; transition: all 0.3s ease; background-color: #000000; color: #00ff00;"
             onmouseover="this.style.borderColor='#00ff00'; this.style.backgroundColor='#333333';"
             onmouseout="this.style.borderColor='#666666'; this.style.backgroundColor='#000000';"
             id="category-${category.replace(/[^a-zA-Z0-9]/g, '')}">
            ${category}
        </div>
    `).join('');
}

function selectCategory(category) {
    selectedCategory = category;
    selectedSubcategory = null;

    // Update category selection visual
    document.querySelectorAll('[id^="category-"]').forEach(el => {
        el.style.backgroundColor = 'white';
        el.style.color = '#374151';
        el.style.borderColor = '#e5e7eb';
    });

    if (category) {
        const selectedEl = document.getElementById(`category-${category.replace(/[^a-zA-Z0-9]/g, '')}`);
        if (selectedEl) {
            selectedEl.style.backgroundColor = '#667eea';
            selectedEl.style.color = 'white';
            selectedEl.style.borderColor = '#667eea';
        }
    } else {
        const allBtn = document.getElementById('category-all');
        if (allBtn) {
            allBtn.style.backgroundColor = '#667eea';
            allBtn.style.color = 'white';
            allBtn.style.borderColor = '#667eea';
        }
    }

    // Show/hide subcategory filter bar and populate subcategories
    const subcategoryBar = document.getElementById('subcategory-filter-bar');
    if (category) {
        subcategoryBar.style.display = 'block';
        populateSubcategories();
    } else {
        subcategoryBar.style.display = 'none';
    }

    populateProductsGrid();
}

function populateSubcategories() {
    const subcategoriesList = document.getElementById('subcategories-list');

    if (!selectedCategory) {
        subcategoriesList.innerHTML = '';
        return;
    }

    const subcategories = [...new Set(
        demoProducts
            .filter(p => p.category === selectedCategory && p.subcategory)
            .map(p => p.subcategory)
    )];

    if (subcategories.length === 0) {
        subcategoriesList.innerHTML = '<div style="color: #9ca3af; font-size: 14px; padding: 8px 14px; font-style: italic;">No subcategories available</div>';
        return;
    }

    subcategoriesList.innerHTML = subcategories.map(subcategory => `
        <button onclick="selectSubcategory('${subcategory}')"
                id="subcategory-${subcategory.replace(/[^a-zA-Z0-9]/g, '')}"
                style="padding: 6px 14px; border: 2px solid #e5e7eb; background: white; color: #374151; border-radius: 20px; cursor: pointer; font-weight: 500; transition: all 0.3s ease; font-size: 14px;"
                onmouseover="this.style.background='#f3f4f6'"
                onmouseout="this.style.background='white'">
            ${subcategory}
        </button>
    `).join('');
}

function selectSubcategory(subcategory) {
    selectedSubcategory = subcategory;

    // Update subcategory selection visual
    document.querySelectorAll('[id^="subcategory-"]').forEach(el => {
        el.style.backgroundColor = 'white';
        el.style.color = '#374151';
        el.style.borderColor = '#e5e7eb';
    });

    // Update "All Items" button
    const allBtn = document.getElementById('subcategory-all');
    if (allBtn) {
        allBtn.style.backgroundColor = 'white';
        allBtn.style.color = '#374151';
        allBtn.style.borderColor = '#e5e7eb';
    }

    if (subcategory) {
        const selectedEl = document.getElementById(`subcategory-${subcategory.replace(/[^a-zA-Z0-9]/g, '')}`);
        if (selectedEl) {
            selectedEl.style.backgroundColor = '#10b981';
            selectedEl.style.color = 'white';
            selectedEl.style.borderColor = '#10b981';
        }
    } else {
        // If subcategory is null, highlight "All Items" button
        if (allBtn) {
            allBtn.style.backgroundColor = '#10b981';
            allBtn.style.color = 'white';
            allBtn.style.borderColor = '#10b981';
        }
    }

    populateProductsGrid();
}

function getFilteredProducts() {
    if (!selectedCategory) return demoProducts;

    let filtered = demoProducts.filter(p => p.category === selectedCategory);

    if (selectedSubcategory) {
        filtered = filtered.filter(p => p.subcategory === selectedSubcategory);
    }

    return filtered;
}

function getItemQuantityInCart(productName) {
    const item = cartItems.find(item => item.description === productName);
    return item ? item.quantity : 0;
}

function populateItems() {
    const itemsList = document.getElementById('items-list');
    const filteredProducts = getFilteredProducts();

    if (filteredProducts.length === 0) {
        itemsList.innerHTML = `
            <div style="color: #666666; text-align: center; padding: 48px; font-size: 18px;">
                ${selectedCategory ? "No items in this category" : "Select a category to view items"}
            </div>
        `;
        return;
    }

    itemsList.innerHTML = filteredProducts.map(product => {
        const quantityInCart = getItemQuantityInCart(product.name);
        return `
            <div style="display: flex; align-items: center; justify-content: space-between; padding: 16px; border-bottom: 2px solid #333333; transition: background-color 0.3s ease; ${quantityInCart > 0 ? 'background-color: #333333;' : ''}"
                 onmouseover="this.style.backgroundColor='#333333';"
                 onmouseout="this.style.backgroundColor='${quantityInCart > 0 ? '#333333' : 'transparent'}';">
                <!-- Product Info -->
                <div style="flex: 1;">
                    <div style="color: #00ff00; font-weight: bold; font-size: 18px; margin-bottom: 8px;">
                        ${product.name}
                    </div>
                    <div style="color: #ff0000; font-weight: 900; font-size: 20px; margin-bottom: 4px;">
                        $${formatCurrency(product.price)}
                    </div>
                    ${product.subcategory ? `<div style="color: #666666; font-size: 14px;">${product.subcategory}</div>` : ''}
                </div>

                <!-- Quantity and Controls -->
                <div style="display: flex; align-items: center; gap: 12px;">
                    ${quantityInCart > 0 ? `
                        <div style="background-color: #00ff00; color: #000000; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; font-size: 14px; font-weight: 900;">
                            ${quantityInCart}
                        </div>
                    ` : ''}
                    <button onclick="removeItemFromModal('${product.name}')"
                            ${quantityInCart === 0 ? 'disabled' : ''}
                            style="width: 48px; height: 48px; padding: 0; font-size: 14px; font-weight: 900; cursor: ${quantityInCart > 0 ? 'pointer' : 'not-allowed'}; border-radius: 4px; border: 2px solid; background-color: ${quantityInCart > 0 ? '#660000' : '#333333'}; color: ${quantityInCart > 0 ? '#ff0000' : '#666666'}; border-color: ${quantityInCart > 0 ? '#ff0000' : '#666666'}; transition: all 0.3s ease;"
                            ${quantityInCart > 0 ? 'onmouseover="this.style.backgroundColor=\'#990000\';" onmouseout="this.style.backgroundColor=\'#660000\';"' : ''}>
                        −
                    </button>
                    <button onclick="addItemFromModal('${product.name}')"
                            style="width: 48px; height: 48px; padding: 0; font-size: 14px; font-weight: 900; cursor: pointer; border-radius: 4px; border: 2px solid #00ff00; background-color: #006600; color: #00ff00; transition: all 0.3s ease;"
                            onmouseover="this.style.backgroundColor='#009900';" onmouseout="this.style.backgroundColor='#006600';">
                        ✓
                    </button>
                </div>
            </div>
        `;
    }).join('');
}

function addItemFromModal(productName) {
    const product = demoProducts.find(p => p.name === productName);
    if (product) {
        addItemToCart(product);
        populateProductsGrid(); // Refresh the products grid to update quantities
        updateModalFooter();
    }
}

function removeItemFromModal(productName) {
    const product = demoProducts.find(p => p.name === productName);
    if (product) {
        removeItemFromCart(product);
        populateProductsGrid(); // Refresh the products grid to update quantities
        updateModalFooter();
    }
}

function updateModalFooter() {
    const footer = document.getElementById('modal-footer');
    if (footer) {
        footer.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div style="color: #374151; font-weight: 600; font-size: 16px;">
                    Items in Cart: <span style="color: #667eea; font-size: 20px; font-weight: 700;">${cartItems.length}</span>
                </div>
                <div style="color: #374151; font-weight: 600; font-size: 16px;">
                    Total: <span style="color: #10b981; font-size: 24px; font-weight: 700;">$${formatCurrency(calculateTotal())}</span>
                </div>
            </div>
        `;
    }
}

function populateProductsGrid() {
    const productsGrid = document.getElementById('products-grid');
    const noProducts = document.getElementById('no-products');

    if (!productsGrid) return;

    const filteredProducts = getFilteredProducts();

    if (filteredProducts.length === 0) {
        productsGrid.style.display = 'none';
        noProducts.style.display = 'block';
        return;
    }

    productsGrid.style.display = 'grid';
    noProducts.style.display = 'none';

    productsGrid.innerHTML = filteredProducts.map(product => {
        const quantityInCart = getItemQuantityInCart(product.name);
        return `
            <div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden; transition: all 0.3s ease; box-shadow: 0 2px 8px rgba(0,0,0,0.1); ${quantityInCart > 0 ? 'border-color: #667eea; box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);' : ''}" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 25px rgba(0,0,0,0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='${quantityInCart > 0 ? '0 4px 12px rgba(102, 126, 234, 0.15)' : '0 2px 8px rgba(0,0,0,0.1)'}'">
                <!-- Product Image -->
                <div style="position: relative; height: 200px; background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%); display: flex; align-items: center; justify-content: center; overflow: hidden;">
                    <img src="${product.image}" alt="${product.name}" style="width: 100%; height: 100%; object-fit: cover;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div style="display: none; width: 100%; height: 100%; align-items: center; justify-content: center; background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%); color: #9ca3af; font-size: 48px;">📦</div>
                    ${quantityInCart > 0 ? `
                        <div style="position: absolute; top: 12px; right: 12px; background: #667eea; color: white; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; font-size: 14px; font-weight: 700; box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);">
                            ${quantityInCart}
                        </div>
                    ` : ''}
                </div>

                <!-- Product Info -->
                <div style="padding: 16px;">
                    <h3 style="font-size: 16px; font-weight: 600; color: #1f2937; margin: 0 0 8px 0; line-height: 1.4; height: 44px; overflow: hidden; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical;">${product.name}</h3>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                        <span style="font-size: 20px; font-weight: 700; color: #10b981;">$${formatCurrency(product.price)}</span>
                        <span style="font-size: 12px; color: #6b7280; background: #f3f4f6; padding: 4px 8px; border-radius: 12px;">${product.subcategory || product.category}</span>
                    </div>

                    <!-- Action Buttons -->
                    <div style="display: flex; gap: 8px; align-items: center;">
                        <button onclick="removeItemFromModal('${product.name}')" ${quantityInCart === 0 ? 'disabled' : ''} style="flex: 1; padding: 8px; border: 2px solid ${quantityInCart > 0 ? '#ef4444' : '#e5e7eb'}; background: ${quantityInCart > 0 ? '#fef2f2' : '#f9fafb'}; color: ${quantityInCart > 0 ? '#ef4444' : '#9ca3af'}; border-radius: 8px; cursor: ${quantityInCart > 0 ? 'pointer' : 'not-allowed'}; font-weight: 600; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center;" ${quantityInCart > 0 ? `onmouseover="this.style.background='#fee2e2'" onmouseout="this.style.background='#fef2f2'"` : ''}>
                            <span style="font-size: 18px;">−</span>
                        </button>
                        <button onclick="addItemFromModal('${product.name}')" style="flex: 2; padding: 10px; border: 2px solid #10b981; background: #10b981; color: white; border-radius: 8px; cursor: pointer; font-weight: 600; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center; gap: 6px;" onmouseover="this.style.background='#059669'" onmouseout="this.style.background='#10b981'">
                            <span style="font-size: 16px;">+</span>
                            <span>Add to Cart</span>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}
