const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// Global state
let activeTab = "tickets";
let currentUser = null;
let userPermissions = [];

// Current user management
async function loadCurrentUser() {
    try {
        console.log('Theater - Loading current user...');
        currentUser = await ipcRenderer.invoke('get-current-user');

        if (currentUser) {
            userPermissions = currentUser.permissions || [];
            console.log('Theater - User loaded:', {
                username: currentUser.username,
                role: currentUser.role,
                permissionCount: userPermissions.length
            });

            updateOperatorInfo();
            updateExitButton(); // Update button based on permissions
        } else {
            console.error('Theater - No current user returned');
            document.getElementById('current-operator').textContent = 'Error: No User';
        }
    } catch (error) {
        console.error('Error loading current user:', error);
        document.getElementById('current-operator').textContent = 'Error: User Load Failed';
    }
}

function updateOperatorInfo() {
    if (currentUser) {
        const operatorSpan = document.getElementById('current-operator');
        if (operatorSpan) {
            operatorSpan.textContent = currentUser.name || currentUser.username || 'Unknown';
        }
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', async function() {
    updateTime();
    setInterval(updateTime, 1000);
    renderContent();

    // Load current user information
    await loadCurrentUser();

    // Initialize maximize button state
    try {
        const isMaximized = await ipcRenderer.invoke('is-fullscreen');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = isMaximized ? '🗗' : '⛶';
            fullscreenBtn.title = isMaximized ? 'Restore Window' : 'Maximize Window';
        }
    } catch (error) {
        console.error('Error checking initial maximize state:', error);
    }
});

// Time functions
function updateTime() {
    const now = new Date();
    document.getElementById('current-date').textContent = formatDate(now);
    document.getElementById('current-time').textContent = formatTime(now);
}

function formatDate(date) {
    return date.toLocaleDateString("en-US", { 
        year: "numeric", 
        month: "2-digit", 
        day: "2-digit" 
    });
}

function formatTime(date) {
    return date.toLocaleTimeString("en-US", { hour12: false });
}

// Permission checking functions
function hasModuleAccess(moduleId) {
    // Admin has access to everything
    if (currentUser && currentUser.role === 'Admin') {
        return true;
    }

    // Check if user has permission for this module
    return userPermissions && userPermissions.some(perm => perm.module_id === moduleId);
}

function hasPOSAccess() {
    return hasModuleAccess('pos');
}

// Update exit button based on user permissions
function updateExitButton() {
    const exitButton = document.querySelector('.back-btn');

    if (!exitButton) {
        console.error('Theater - Exit button not found');
        return;
    }

    if (hasPOSAccess()) {
        // User has POS access - show "Back to POS" button
        console.log('Theater - User has POS access, showing Back to POS button');
        exitButton.innerHTML = `
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="19" y1="12" x2="5" y2="12"/>
                <polyline points="12,19 5,12 12,5"/>
            </svg>
            Back to POS
        `;
        exitButton.title = 'Return to Point of Sale System';
    } else {
        // User has NO POS access - show "LOGOUT" button
        console.log('Theater - User has NO POS access, showing Logout button');
        exitButton.innerHTML = `
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
                <polyline points="16,17 21,12 16,7"/>
                <line x1="21" y1="12" x2="9" y2="12"/>
            </svg>
            LOGOUT
        `;
        exitButton.title = 'Logout from Theater Management System';
    }
}

// Navigation functions
function handleExit() {
    if (hasPOSAccess()) {
        // User has POS access - navigate to POS
        console.log('Theater - Navigating back to POS');
        ipcRenderer.invoke('navigate-to', 'pos');
    } else {
        // User has NO POS access - logout
        console.log('Theater - User has no POS access, logging out');
        handleLogout();
    }
}

async function handleLogout() {
    try {
        console.log('Theater - Logging out...');
        const result = await ipcRenderer.invoke('logout');
        if (result.success) {
            console.log('Theater - Logout successful');
        } else {
            console.error('Theater - Logout failed:', result.message);
        }
    } catch (error) {
        console.error('Theater - Logout error:', error);
    }
}

// Window control functions
function closeApp() {
    ipcRenderer.invoke('close-app');
}

function minimizeApp() {
    ipcRenderer.invoke('minimize-app');
}

async function toggleFullscreen() {
    try {
        const isMaximized = await ipcRenderer.invoke('toggle-fullscreen');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = isMaximized ? '🗗' : '⛶';
            fullscreenBtn.title = isMaximized ? 'Restore Window' : 'Maximize Window';
        }
    } catch (error) {
        console.error('Error toggling maximize:', error);
    }
}

function setActiveTab(tabId) {
    activeTab = tabId;
    
    // Update tab visual states
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    const activeTabBtn = document.getElementById(`tab-${tabId}`);
    if (activeTabBtn && !activeTabBtn.classList.contains('exit')) {
        activeTabBtn.classList.add('active');
    }
    
    renderContent();
}

// Content rendering functions
function renderContent() {
    const contentArea = document.getElementById('content-area');
    
    switch (activeTab) {
        case "tickets":
            contentArea.innerHTML = renderTicketsContent();
            break;
        case "current":
            contentArea.innerHTML = renderCurrentViewersContent();
            break;
        case "banned":
            contentArea.innerHTML = renderBannedViewersContent();
            break;
        default:
            contentArea.innerHTML = renderTicketsContent();
    }
}

function renderTicketsContent() {
    return `
        <div>
            <h2 class="content-title">Issue Tickets</h2>
            <p class="content-description">Manage ticket issuance for theater access</p>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 24px; margin-bottom: 32px;">
                <div style="background: linear-gradient(135deg, #10b981, #059669); color: #ffffff; border-radius: 12px; padding: 24px; box-shadow: 0 10px 15px -3px rgba(16, 185, 129, 0.4);">
                    <h3 style="font-size: 20px; font-weight: bold; margin-bottom: 8px;">Active Tickets</h3>
                    <p style="font-size: 36px; font-weight: 900; margin-bottom: 8px;">24</p>
                    <p style="opacity: 0.9;">Currently in use</p>
                </div>
                <div style="background: linear-gradient(135deg, #3b82f6, #2563eb); color: #ffffff; border-radius: 12px; padding: 24px; box-shadow: 0 10px 15px -3px rgba(59, 130, 246, 0.4);">
                    <h3 style="font-size: 20px; font-weight: bold; margin-bottom: 8px;">Available Tickets</h3>
                    <p style="font-size: 36px; font-weight: 900; margin-bottom: 8px;">76</p>
                    <p style="opacity: 0.9;">Ready to issue</p>
                </div>
                <div style="background: linear-gradient(135deg, #f59e0b, #d97706); color: #ffffff; border-radius: 12px; padding: 24px; box-shadow: 0 10px 15px -3px rgba(245, 158, 11, 0.4);">
                    <h3 style="font-size: 20px; font-weight: bold; margin-bottom: 8px;">Total Capacity</h3>
                    <p style="font-size: 36px; font-weight: 900; margin-bottom: 8px;">100</p>
                    <p style="opacity: 0.9;">Maximum viewers</p>
                </div>
            </div>
            
            <div style="background-color: #f9fafb; border: 2px solid #e5e7eb; border-radius: 12px; padding: 32px;">
                <h3 style="font-size: 24px; font-weight: bold; color: #1f2937; margin-bottom: 24px; text-align: center;">Ticket Management</h3>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 24px;">
                    <button style="background: linear-gradient(135deg, #10b981, #059669); color: #ffffff; border: none; border-radius: 8px; padding: 16px 24px; font-size: 16px; font-weight: bold; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 10px 15px -3px rgba(0, 0, 0, 0.1)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 6px -1px rgba(0, 0, 0, 0.1)'">
                        Issue New Ticket
                    </button>
                    <button style="background: linear-gradient(135deg, #3b82f6, #2563eb); color: #ffffff; border: none; border-radius: 8px; padding: 16px 24px; font-size: 16px; font-weight: bold; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 10px 15px -3px rgba(0, 0, 0, 0.1)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 6px -1px rgba(0, 0, 0, 0.1)'">
                        Validate Ticket
                    </button>
                    <button style="background: linear-gradient(135deg, #f59e0b, #d97706); color: #ffffff; border: none; border-radius: 8px; padding: 16px 24px; font-size: 16px; font-weight: bold; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 10px 15px -3px rgba(0, 0, 0, 0.1)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 6px -1px rgba(0, 0, 0, 0.1)'">
                        Revoke Ticket
                    </button>
                    <button style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: #ffffff; border: none; border-radius: 8px; padding: 16px 24px; font-size: 16px; font-weight: bold; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 10px 15px -3px rgba(0, 0, 0, 0.1)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 6px -1px rgba(0, 0, 0, 0.1)'">
                        Print Report
                    </button>
                </div>
                
                <div class="placeholder-content">
                    <svg class="placeholder-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M2 6a2 2 0 012-2h16a2 2 0 012 2v2H2V6zM2 10h20v8a2 2 0 01-2 2H4a2 2 0 01-2-2v-8z"/>
                    </svg>
                    <p class="placeholder-text">Ticket management interface will be implemented here</p>
                </div>
            </div>
        </div>
    `;
}

function renderCurrentViewersContent() {
    return `
        <div>
            <h2 class="content-title">Current Viewer List</h2>
            <p class="content-description">View and manage currently active theater viewers</p>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 24px; margin-bottom: 32px;">
                <div style="background: linear-gradient(135deg, #10b981, #059669); color: #ffffff; border-radius: 12px; padding: 24px; box-shadow: 0 10px 15px -3px rgba(16, 185, 129, 0.4);">
                    <h3 style="font-size: 20px; font-weight: bold; margin-bottom: 8px;">Active Viewers</h3>
                    <p style="font-size: 36px; font-weight: 900; margin-bottom: 8px;">24</p>
                    <p style="opacity: 0.9;">Currently watching</p>
                </div>
                <div style="background: linear-gradient(135deg, #3b82f6, #2563eb); color: #ffffff; border-radius: 12px; padding: 24px; box-shadow: 0 10px 15px -3px rgba(59, 130, 246, 0.4);">
                    <h3 style="font-size: 20px; font-weight: bold; margin-bottom: 8px;">VIP Viewers</h3>
                    <p style="font-size: 36px; font-weight: 900; margin-bottom: 8px;">5</p>
                    <p style="opacity: 0.9;">Premium access</p>
                </div>
                <div style="background: linear-gradient(135deg, #f59e0b, #d97706); color: #ffffff; border-radius: 12px; padding: 24px; box-shadow: 0 10px 15px -3px rgba(245, 158, 11, 0.4);">
                    <h3 style="font-size: 20px; font-weight: bold; margin-bottom: 8px;">Average Duration</h3>
                    <p style="font-size: 36px; font-weight: 900; margin-bottom: 8px;">45m</p>
                    <p style="opacity: 0.9;">Per session</p>
                </div>
            </div>
            
            <div style="background-color: #f9fafb; border: 2px solid #e5e7eb; border-radius: 12px; padding: 32px;">
                <h3 style="font-size: 24px; font-weight: bold; color: #1f2937; margin-bottom: 24px; text-align: center;">Viewer Management</h3>
                
                <div class="placeholder-content">
                    <svg class="placeholder-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20 21v-2a4 4 0 00-4-4H8a4 4 0 00-4 4v2"/>
                        <circle cx="12" cy="7" r="4"/>
                    </svg>
                    <p class="placeholder-text">Current viewer list interface will be implemented here</p>
                </div>
            </div>
        </div>
    `;
}

function renderBannedViewersContent() {
    return `
        <div>
            <h2 class="content-title">Banned Viewer List</h2>
            <p class="content-description">Manage banned viewers and access restrictions</p>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 24px; margin-bottom: 32px;">
                <div style="background: linear-gradient(135deg, #ef4444, #dc2626); color: #ffffff; border-radius: 12px; padding: 24px; box-shadow: 0 10px 15px -3px rgba(239, 68, 68, 0.4);">
                    <h3 style="font-size: 20px; font-weight: bold; margin-bottom: 8px;">Banned Users</h3>
                    <p style="font-size: 36px; font-weight: 900; margin-bottom: 8px;">3</p>
                    <p style="opacity: 0.9;">Access denied</p>
                </div>
                <div style="background: linear-gradient(135deg, #f59e0b, #d97706); color: #ffffff; border-radius: 12px; padding: 24px; box-shadow: 0 10px 15px -3px rgba(245, 158, 11, 0.4);">
                    <h3 style="font-size: 20px; font-weight: bold; margin-bottom: 8px;">Temporary Bans</h3>
                    <p style="font-size: 36px; font-weight: 900; margin-bottom: 8px;">1</p>
                    <p style="opacity: 0.9;">Time-limited</p>
                </div>
                <div style="background: linear-gradient(135deg, #6b7280, #4b5563); color: #ffffff; border-radius: 12px; padding: 24px; box-shadow: 0 10px 15px -3px rgba(107, 114, 128, 0.4);">
                    <h3 style="font-size: 20px; font-weight: bold; margin-bottom: 8px;">Permanent Bans</h3>
                    <p style="font-size: 36px; font-weight: 900; margin-bottom: 8px;">2</p>
                    <p style="opacity: 0.9;">Indefinite</p>
                </div>
            </div>
            
            <div style="background-color: #f9fafb; border: 2px solid #e5e7eb; border-radius: 12px; padding: 32px;">
                <h3 style="font-size: 24px; font-weight: bold; color: #1f2937; margin-bottom: 24px; text-align: center;">Ban Management</h3>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 24px;">
                    <button style="background: linear-gradient(135deg, #ef4444, #dc2626); color: #ffffff; border: none; border-radius: 8px; padding: 16px 24px; font-size: 16px; font-weight: bold; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 10px 15px -3px rgba(0, 0, 0, 0.1)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 6px -1px rgba(0, 0, 0, 0.1)'">
                        Add Ban
                    </button>
                    <button style="background: linear-gradient(135deg, #f59e0b, #d97706); color: #ffffff; border: none; border-radius: 8px; padding: 16px 24px; font-size: 16px; font-weight: bold; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 10px 15px -3px rgba(0, 0, 0, 0.1)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 6px -1px rgba(0, 0, 0, 0.1)'">
                        Temporary Ban
                    </button>
                    <button style="background: linear-gradient(135deg, #10b981, #059669); color: #ffffff; border: none; border-radius: 8px; padding: 16px 24px; font-size: 16px; font-weight: bold; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 10px 15px -3px rgba(0, 0, 0, 0.1)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 6px -1px rgba(0, 0, 0, 0.1)'">
                        Remove Ban
                    </button>
                    <button style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: #ffffff; border: none; border-radius: 8px; padding: 16px 24px; font-size: 16px; font-weight: bold; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 10px 15px -3px rgba(0, 0, 0, 0.1)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 6px -1px rgba(0, 0, 0, 0.1)'">
                        Export List
                    </button>
                </div>
                
                <div class="placeholder-content">
                    <svg class="placeholder-icon" viewBox="0 0 24 24" fill="currentColor">
                        <circle cx="12" cy="12" r="10"/>
                        <line x1="4.93" y1="4.93" x2="19.07" y2="19.07"/>
                    </svg>
                    <p class="placeholder-text">Banned viewer list interface will be implemented here</p>
                </div>
            </div>
        </div>
    `;
}
