<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Set Up Category - Rainbow Station Inc</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: #f9fafb;
            color: #1f2937;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            height: 100vh;
            overflow: hidden;
        }

        .page-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* Header */
        .main-header {
            background-color: #ffffff;
            border-bottom: 2px solid #e5e7eb;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            flex-shrink: 0;
            padding: 16px 24px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title h1 {
            font-size: 24px;
            font-weight: 900;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .header-title p {
            font-size: 14px;
            color: #6b7280;
        }

        .header-info {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .header-info-text {
            display: flex;
            flex-direction: column;
            gap: 4px;
            font-size: 14px;
            color: #374151;
        }

        .header-controls {
            display: flex;
            gap: 8px;
        }

        .header-control-btn {
            background-color: #374151;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: background-color 0.2s;
        }

        .header-control-btn:hover {
            background-color: #4b5563;
        }

        .header-control-btn.minimize:hover {
            background-color: #3b82f6;
        }

        .header-control-btn.close:hover {
            background-color: #ef4444;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            overflow-y: auto;
            padding: 24px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 900;
            color: #1f2937;
            margin-bottom: 24px;
        }

        .grid {
            display: grid;
        }

        .grid-cols-2 {
            grid-template-columns: repeat(2, 1fr);
        }

        .gap-6 {
            gap: 24px;
        }

        .gap-4 {
            gap: 16px;
        }

        .gap-2 {
            gap: 8px;
        }

        .mb-8 {
            margin-bottom: 32px;
        }

        .mb-6 {
            margin-bottom: 24px;
        }

        .mb-4 {
            margin-bottom: 16px;
        }

        .mb-2 {
            margin-bottom: 8px;
        }

        .card {
            background-color: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 16px;
        }

        .card-title.green {
            color: #059669;
        }

        .card-title.blue {
            color: #2563eb;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: bold;
            color: #374151;
            margin-bottom: 8px;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s, box-shadow 0.2s;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #059669;
            box-shadow: 0 0 0 2px rgba(5, 150, 105, 0.2);
        }

        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .btn-green {
            background-color: #059669;
            color: white;
        }

        .btn-green:hover {
            background-color: #047857;
        }

        .btn-blue {
            background-color: #2563eb;
            color: white;
        }

        .btn-blue:hover {
            background-color: #1d4ed8;
        }

        .btn-red {
            background-color: #dc2626;
            color: white;
        }

        .btn-red:hover {
            background-color: #b91c1c;
        }

        .btn-full {
            width: 100%;
        }

        .hierarchy-item {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 12px;
        }

        .hierarchy-title {
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .hierarchy-children {
            margin-left: 16px;
        }

        .hierarchy-child {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 4px;
        }

        .table-container {
            overflow-x: auto;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background-color: #f9fafb;
            border-bottom: 2px solid #e5e7eb;
            padding: 12px;
            text-align: left;
            font-weight: bold;
            color: #374151;
        }

        .table td {
            padding: 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tr:hover {
            background-color: #f9fafb;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-active {
            background-color: #d1fae5;
            color: #065f46;
        }

        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
        }

        .flex {
            display: flex;
        }

        .justify-between {
            justify-content: space-between;
        }

        .items-center {
            align-items: center;
        }

        .space-y-4 > * + * {
            margin-top: 16px;
        }

        .space-y-3 > * + * {
            margin-top: 12px;
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- Header -->
        <div class="main-header">
            <div class="header-content">
                <div class="header-title">
                    <h1>SET UP CATEGORY</h1>
                    <p>Rainbow Station Inc.</p>
                </div>
                <div class="header-info">
                    <div class="header-info-text">
                        <div>Date: <span id="current-date">01/01/24</span></div>
                        <div>Time: <span id="current-time">00:00:00</span></div>
                        <div>Admin: <span>Simon</span></div>
                    </div>
                    <div class="header-controls">
                        <button onclick="navigateToAdmin()" class="header-control-btn" style="background-color: #059669;" title="Back to Admin">← ADMIN</button>
                        <button onclick="toggleFullscreen()" id="fullscreen-btn" class="header-control-btn" title="Maximize Window">⛶</button>
                        <button onclick="minimizeApp()" class="header-control-btn minimize" title="Minimize">−</button>
                        <button onclick="closeApp()" class="header-control-btn close" title="Close">×</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="grid grid-cols-2 gap-6 mb-8">
                <!-- Add New Category Card -->
                <div class="card">
                    <h3 class="card-title green">Add New Category</h3>
                    <div class="space-y-4">
                        <div class="form-group">
                            <label class="form-label">Category Name</label>
                            <input type="text" class="form-input" id="categoryName" placeholder="Enter category name">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Category Code</label>
                            <input type="text" class="form-input" id="categoryCode" placeholder="Enter category code">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Parent Category</label>
                            <select class="form-select" id="parentCategory">
                                <option value="">Select Parent Category (Optional)</option>
                                <option value="A">A. Pipes</option>
                                <option value="B">B. Nailpolish Remo</option>
                                <option value="C">C. Miscellaneous</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Description</label>
                            <textarea class="form-textarea" id="categoryDescription" placeholder="Enter category description"></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Display Order</label>
                            <input type="number" class="form-input" id="displayOrder" placeholder="Enter display order" min="1">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Status</label>
                            <select class="form-select" id="categoryStatus">
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                        <button class="btn btn-green btn-full" onclick="addCategory()">Add Category</button>
                    </div>
                </div>

                <!-- Category Hierarchy Card -->
                <div class="card">
                    <h3 class="card-title blue">Category Hierarchy</h3>
                    <div class="space-y-3" id="categoryHierarchy">
                        <!-- Hierarchy items will be populated by JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Category Management Table -->
            <div class="card">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="card-title">Category Management</h3>
                    <div class="flex gap-2">
                        <button class="btn btn-blue" onclick="importCategories()">Import Categories</button>
                        <button class="btn btn-green" onclick="exportCategories()">Export Categories</button>
                    </div>
                </div>
                
                <div class="table-container">
                    <table class="table" id="categoryTable">
                        <thead>
                            <tr>
                                <th>Code</th>
                                <th>Category Name</th>
                                <th>Parent Category</th>
                                <th>Products Count</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="categoryTableBody">
                            <!-- Table rows will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="setup-category.js"></script>
</body>
</html>
