<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Set Up Product - Rainbow Station Inc</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: #f9fafb;
            color: #1f2937;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            height: 100vh;
            overflow: hidden;
        }

        .page-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* Header */
        .main-header {
            background-color: #ffffff;
            border-bottom: 2px solid #e5e7eb;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            flex-shrink: 0;
            padding: 16px 24px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title h1 {
            font-size: 24px;
            font-weight: 900;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .header-title p {
            font-size: 14px;
            color: #6b7280;
        }

        .header-info {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .header-info-text {
            display: flex;
            flex-direction: column;
            gap: 4px;
            font-size: 14px;
            color: #374151;
        }

        .header-controls {
            display: flex;
            gap: 8px;
        }

        .header-control-btn {
            background-color: #374151;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: background-color 0.2s;
        }

        .header-control-btn:hover {
            background-color: #4b5563;
        }

        .header-control-btn.minimize:hover {
            background-color: #3b82f6;
        }

        .header-control-btn.close:hover {
            background-color: #ef4444;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            overflow-y: auto;
            padding: 24px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 900;
            color: #1f2937;
            margin-bottom: 24px;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
            margin-bottom: 24px;
        }

        .btn {
            padding: 8px 32px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .btn-green {
            background-color: #059669;
            color: white;
        }

        .btn-green:hover {
            background-color: #047857;
        }

        .btn-blue {
            background-color: #2563eb;
            color: white;
        }

        .btn-blue:hover {
            background-color: #1d4ed8;
        }

        .btn-red {
            background-color: #dc2626;
            color: white;
        }

        .btn-red:hover {
            background-color: #b91c1c;
        }

        .btn-gray {
            background-color: #4b5563;
            color: white;
        }

        .btn-gray:hover {
            background-color: #374151;
        }

        .btn-small {
            padding: 4px 12px;
            font-size: 12px;
        }

        .form-card {
            background-color: #ffffff;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            padding: 24px;
        }

        .form-grid {
            display: grid;
            gap: 24px;
        }

        .form-row {
            display: grid;
            gap: 16px;
        }

        .form-row-2 {
            grid-template-columns: repeat(2, 1fr);
        }

        .form-row-3 {
            grid-template-columns: repeat(3, 1fr);
        }

        .form-row-12 {
            grid-template-columns: repeat(12, 1fr);
        }

        .col-span-3 {
            grid-column: span 3;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-size: 14px;
            font-weight: bold;
            color: #374151;
            margin-bottom: 8px;
        }

        .form-input, .form-select, .form-textarea {
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s, box-shadow 0.2s;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #059669;
            box-shadow: 0 0 0 2px rgba(5, 150, 105, 0.2);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .input-with-button {
            display: flex;
            gap: 8px;
        }

        .input-with-button .form-input {
            flex: 1;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .checkbox {
            width: 20px;
            height: 20px;
        }

        .image-upload {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 32px;
            text-align: center;
            background-color: #f9fafb;
        }

        .image-upload-icon {
            width: 48px;
            height: 48px;
            margin: 0 auto 16px;
            color: #9ca3af;
        }

        .image-upload-text {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 16px;
        }

        .image-upload-buttons {
            display: flex;
            gap: 8px;
            justify-content: center;
        }

        .table-container {
            border: 1px solid #d1d5db;
            border-radius: 8px;
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background-color: #f3f4f6;
            border-bottom: 1px solid #d1d5db;
            padding: 12px;
            text-align: left;
            font-weight: bold;
            color: #374151;
            border-right: 1px solid #d1d5db;
        }

        .table th:last-child {
            border-right: none;
        }

        .table th.text-center {
            text-align: center;
        }

        .table td {
            padding: 8px 12px;
            border-bottom: 1px solid #e5e7eb;
            border-right: 1px solid #d1d5db;
            color: #1f2937;
        }

        .table td:last-child {
            border-right: none;
        }

        .table tr:hover {
            background-color: #f9fafb;
        }

        .table td.font-medium {
            font-weight: 500;
        }

        .table td.text-center {
            text-align: center;
        }

        .table .form-input {
            width: 80px;
            text-align: center;
            padding: 4px 8px;
        }

        .table .form-input.price {
            width: 96px;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 16px;
        }

        .grid-2 {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 24px;
        }

        .space-y-4 > * + * {
            margin-top: 16px;
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- Header -->
        <div class="main-header">
            <div class="header-content">
                <div class="header-title">
                    <h1>SET UP PRODUCT</h1>
                    <p>Rainbow Station Inc.</p>
                </div>
                <div class="header-info">
                    <div class="header-info-text">
                        <div>Date: <span id="current-date">01/01/24</span></div>
                        <div>Time: <span id="current-time">00:00:00</span></div>
                        <div>Admin: <span>Simon</span></div>
                    </div>
                    <div class="header-controls">
                        <button onclick="navigateToAdmin()" class="header-control-btn" style="background-color: #059669;" title="Back to Admin">← ADMIN</button>
                        <button onclick="toggleFullscreen()" id="fullscreen-btn" class="header-control-btn" title="Maximize Window">⛶</button>
                        <button onclick="minimizeApp()" class="header-control-btn minimize" title="Minimize">−</button>
                        <button onclick="closeApp()" class="header-control-btn close" title="Close">×</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Action Buttons -->
            <div class="action-buttons">
                <button class="btn btn-green" onclick="saveProduct()">SAVE</button>
                <button class="btn btn-blue" onclick="clearForm()">CLEAR</button>
                <button class="btn btn-red" onclick="deleteProduct()">DELETE</button>
            </div>

            <!-- Main Form -->
            <div class="form-card">
                <div class="form-grid">
                    <!-- Barcode Row -->
                    <div class="form-row form-row-12">
                        <div class="form-group col-span-3">
                            <label class="form-label">Barcode</label>
                            <div class="input-with-button">
                                <input type="text" class="form-input" id="barcode" placeholder="Enter barcode">
                                <button class="btn btn-gray btn-small">🔍</button>
                            </div>
                        </div>
                    </div>

                    <!-- Description Row -->
                    <div class="form-group">
                        <label class="form-label">Description</label>
                        <textarea class="form-textarea" id="description" placeholder="Enter product description"></textarea>
                    </div>

                    <!-- Category and Sub Category Row -->
                    <div class="form-row form-row-2">
                        <div class="form-group">
                            <label class="form-label">Category</label>
                            <select class="form-select" id="category">
                                <option value="">Select Category</option>
                                <option value="A-pipes">A. Pipes</option>
                                <option value="A-deli">A. Deli</option>
                                <option value="A-pills">A. Pills</option>
                                <option value="B-nailpolish">B. Nailpolish Remo</option>
                                <option value="black-unicorn">Black Unicorn</option>
                                <option value="C-misc">C. Miscellaneous</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Sub Category</label>
                            <select class="form-select" id="subCategory">
                                <option value="">Select Sub Category</option>
                                <option value="water-pipes">Water Pipes</option>
                                <option value="dry-pipes">Dry Pipes</option>
                                <option value="metal-pipes">Metal Pipes</option>
                                <option value="glass-pipes">Glass Pipes</option>
                            </select>
                        </div>
                    </div>

                    <!-- Supplier and Purchase Price Row -->
                    <div class="form-row form-row-2">
                        <div class="form-group">
                            <label class="form-label">Supplier</label>
                            <div class="input-with-button">
                                <select class="form-select" id="supplier">
                                    <option value="">Select Supplier</option>
                                    <option value="SUP001">ABC Wholesale</option>
                                    <option value="SUP002">XYZ Distributors</option>
                                    <option value="SUP003">Premium Supplies Co</option>
                                </select>
                                <button class="btn btn-gray btn-small">🔍</button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Purchase Price</label>
                            <input type="number" step="0.01" class="form-input" id="purchasePrice" placeholder="0.00">
                        </div>
                    </div>

                    <!-- Style and Color Row -->
                    <div class="form-row form-row-2">
                        <div class="form-group">
                            <label class="form-label">Style</label>
                            <input type="text" class="form-input" id="style" placeholder="Enter style">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Color</label>
                            <div class="input-with-button">
                                <input type="text" class="form-input" id="color" placeholder="Enter color">
                                <button class="btn btn-gray btn-small">▼</button>
                            </div>
                        </div>
                    </div>

                    <!-- Size, Min Qty, Max Qty Row -->
                    <div class="form-row form-row-3">
                        <div class="form-group">
                            <label class="form-label">Size</label>
                            <input type="text" class="form-input" id="size" placeholder="Enter size">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Min Qty</label>
                            <input type="number" class="form-input" id="minQty" placeholder="0">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Max Qty</label>
                            <input type="number" class="form-input" id="maxQty" placeholder="0">
                        </div>
                    </div>

                    <!-- Image and Checkboxes Section -->
                    <div class="grid-2">
                        <!-- Image Section -->
                        <div>
                            <label class="form-label">Image</label>
                            <div class="image-upload">
                                <div class="image-upload-icon">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 48 48">
                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                </div>
                                <p class="image-upload-text">No image selected</p>
                                <div class="image-upload-buttons">
                                    <button class="btn btn-blue btn-small">BROWSE</button>
                                    <button class="btn btn-gray btn-small">CLEAR</button>
                                </div>
                            </div>
                        </div>

                        <!-- Checkboxes Section -->
                        <div class="space-y-4">
                            <div class="checkbox-group">
                                <input type="checkbox" class="checkbox" id="specialDiscount">
                                <label class="form-label" for="specialDiscount">Special Discount</label>
                            </div>
                            <div class="checkbox-group">
                                <input type="checkbox" class="checkbox" id="priority">
                                <label class="form-label" for="priority">Priority</label>
                            </div>
                            <div class="checkbox-group">
                                <input type="checkbox" class="checkbox" id="imageConfirm">
                                <label class="form-label" for="imageConfirm">Image Confirm</label>
                            </div>
                            <div class="checkbox-group">
                                <input type="checkbox" class="checkbox" id="nonScanable">
                                <label class="form-label" for="nonScanable">Non Scanable</label>
                            </div>
                            <div class="checkbox-group">
                                <input type="checkbox" class="checkbox" id="deliItem">
                                <label class="form-label" for="deliItem">Deli Item</label>
                            </div>
                        </div>
                    </div>

                    <!-- Selling Price Section -->
                    <div>
                        <label class="section-title">Selling Price</label>
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Location</th>
                                        <th class="text-center">Stock</th>
                                        <th class="text-center">Price</th>
                                    </tr>
                                </thead>
                                <tbody id="locationStockTable">
                                    <!-- Table rows will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="setup-product.js"></script>
</body>
</html>
