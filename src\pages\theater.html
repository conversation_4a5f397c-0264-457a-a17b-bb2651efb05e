<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theater Management - Rainbow Station Inc</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: #ffffff;
            color: #1f2937;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 50;
            background: linear-gradient(to right, #1e3a8a, #3730a3, #1e3a8a);
            color: #ffffff;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .header-content {
            width: 100%;
            max-width: none;
            padding: 16px;
        }

        .header-flex {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: space-between;
            gap: 16px;
        }

        @media (min-width: 640px) {
            .header-flex {
                flex-direction: row;
                align-items: center;
            }
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .back-btn {
            color: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.3);
            background-color: rgba(255, 255, 255, 0.1);
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-weight: 600;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .back-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .header-title h1 {
            font-size: 20px;
            font-weight: 900;
            color: #ffffff;
            letter-spacing: 0.05em;
            margin-bottom: 4px;
        }

        @media (min-width: 640px) {
            .header-title h1 {
                font-size: 24px;
            }
        }

        @media (min-width: 1024px) {
            .header-title h1 {
                font-size: 32px;
            }
        }

        .header-title p {
            font-size: 14px;
            color: #bfdbfe;
            font-weight: 500;
        }

        @media (min-width: 640px) {
            .header-title p {
                font-size: 16px;
            }
        }

        .header-right {
            text-align: right;
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 16px;
            flex-shrink: 0;
        }

        .header-info {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 2px;
        }

        .header-info div {
            font-size: 12px;
            font-weight: bold;
            color: #bfdbfe;
            white-space: nowrap;
        }

        .header-info span {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 2px 8px;
            font-size: 14px;
            font-weight: 900;
            color: #ffffff;
            border-radius: 4px;
            margin-left: 6px;
        }

        .header-controls {
            display: flex;
            gap: 4px;
            align-items: center;
            flex-shrink: 0;
        }

        .header-control-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            transition: all 0.3s ease;
            min-width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .header-control-btn.minimize {
            background: rgba(245, 158, 11, 0.2);
            border-color: rgba(245, 158, 11, 0.5);
        }

        .header-control-btn.close {
            background: rgba(239, 68, 68, 0.2);
            border-color: rgba(239, 68, 68, 0.5);
        }

        .header-control-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            background: rgba(255, 255, 255, 0.2);
        }

        /* Main Content */
        .main-content {
            margin-top: 120px;
            padding: 32px 16px;
        }

        /* Tab Navigation */
        .tab-navigation {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 32px;
            justify-content: center;
        }

        @media (min-width: 768px) {
            .tab-navigation {
                justify-content: flex-start;
            }
        }

        .tab-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 16px 24px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            background-color: #ffffff;
            color: #374151;
            font-weight: bold;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .tab-btn:hover {
            border-color: #3b82f6;
            color: #3b82f6;
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        .tab-btn.active {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: #ffffff;
            border-color: #3b82f6;
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(59, 130, 246, 0.4);
        }

        .tab-btn.exit {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: #ffffff;
            border-color: #ef4444;
        }

        .tab-btn.exit:hover {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            border-color: #dc2626;
        }

        .tab-icon {
            width: 20px;
            height: 20px;
            fill: currentColor;
        }

        /* Content Area */
        .content-area {
            background-color: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 32px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            min-height: 400px;
        }

        .content-title {
            font-size: 28px;
            font-weight: 900;
            color: #1f2937;
            margin-bottom: 16px;
            text-align: center;
        }

        .content-description {
            font-size: 18px;
            color: #6b7280;
            text-align: center;
            margin-bottom: 32px;
        }

        .placeholder-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 64px 32px;
            text-align: center;
        }

        .placeholder-icon {
            width: 80px;
            height: 80px;
            color: #d1d5db;
            margin-bottom: 24px;
        }

        .placeholder-text {
            font-size: 20px;
            color: #9ca3af;
            font-weight: 600;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .header-flex {
                flex-direction: column;
                gap: 12px;
                align-items: flex-start;
            }

            .header-right {
                width: 100%;
                justify-content: space-between;
            }

            .header-info div {
                font-size: 11px;
            }

            .header-info span {
                font-size: 12px;
                padding: 1px 6px;
            }

            .header-control-btn {
                padding: 3px 6px;
                font-size: 11px;
                min-width: 24px;
                height: 24px;
            }
        }

        @media (max-width: 640px) {
            .main-content {
                margin-top: 140px;
                padding: 16px;
            }

            .content-area {
                padding: 24px 16px;
            }

            .tab-btn {
                padding: 12px 16px;
                font-size: 14px;
            }

            .content-title {
                font-size: 24px;
            }

            .content-description {
                font-size: 16px;
            }

            .header-right {
                flex-direction: column;
                gap: 8px;
                align-items: flex-end;
            }

            .header-controls {
                gap: 6px;
            }

            .header-control-btn {
                padding: 4px 6px;
                font-size: 10px;
                min-width: 26px;
                height: 26px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <div class="header-flex">
                <div class="header-left">
                    <button class="back-btn" onclick="handleExit()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="19" y1="12" x2="5" y2="12"/>
                            <polyline points="12,19 5,12 12,5"/>
                        </svg>
                        Back to POS
                    </button>
                    <div class="header-title">
                        <h1>THEATER MANAGEMENT SYSTEM</h1>
                        <p>Rainbow Station Inc.</p>
                    </div>
                </div>
                <div class="header-right">
                    <div class="header-info">
                        <div>Date: <span id="current-date">01/01/24</span></div>
                        <div>Time: <span id="current-time">00:00:00</span></div>
                        <div>Operator: <span id="current-operator">Loading...</span></div>
                    </div>
                    <div class="header-controls">
                        <button onclick="toggleFullscreen()" id="fullscreen-btn" class="header-control-btn" title="Maximize Window">⛶</button>
                        <button onclick="minimizeApp()" class="header-control-btn minimize" title="Minimize">−</button>
                        <button onclick="closeApp()" class="header-control-btn close" title="Close">×</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Tab Navigation -->
        <div class="tab-navigation">
            <button class="tab-btn active" onclick="setActiveTab('tickets')" id="tab-tickets">
                <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M2 6a2 2 0 012-2h16a2 2 0 012 2v2H2V6zM2 10h20v8a2 2 0 01-2 2H4a2 2 0 01-2-2v-8z"/>
                </svg>
                ISSUE TICKETS
            </button>
            <button class="tab-btn" onclick="setActiveTab('current')" id="tab-current">
                <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 21v-2a4 4 0 00-4-4H8a4 4 0 00-4 4v2"/>
                    <circle cx="12" cy="7" r="4"/>
                </svg>
                CURRENT VIEWER LIST
            </button>
            <button class="tab-btn" onclick="setActiveTab('banned')" id="tab-banned">
                <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                    <circle cx="12" cy="12" r="10"/>
                    <line x1="4.93" y1="4.93" x2="19.07" y2="19.07"/>
                </svg>
                BANNED VIEWER LIST
            </button>
            <button class="tab-btn exit" onclick="handleExit()" id="tab-exit">
                <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                    <line x1="18" y1="6" x2="6" y2="18"/>
                    <line x1="6" y1="6" x2="18" y2="18"/>
                </svg>
                EXIT
            </button>
        </div>

        <!-- Content Area -->
        <div class="content-area" id="content-area">
            <!-- Content will be loaded here -->
        </div>
    </div>

    <script src="theater.js"></script>
</body>
</html>
