<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - Rainbow Station Inc</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: #f9fafb;
            color: #1f2937;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            height: 100vh;
            overflow: hidden;
        }

        .admin-container {
            height: 100vh;
            display: flex;
            overflow: hidden;
        }

        /* Sidebar */
        .sidebar {
            background-color: #ffffff;
            border-right: 2px solid #e5e7eb;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            flex-shrink: 0;
            width: 256px;
        }

        .sidebar.collapsed {
            width: 64px;
        }

        .sidebar-header {
            padding: 16px;
            border-bottom: 1px solid #e5e7eb;
            height: 80px;
            display: flex;
            align-items: center;
        }

        .sidebar-header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 900;
            color: #1f2937;
        }

        .sidebar.collapsed .sidebar-title {
            display: none;
        }

        .toggle-btn {
            background-color: #ffffff;
            color: #10b981;
            border: 1px solid #10b981;
            padding: 8px;
            cursor: pointer;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .toggle-btn:hover {
            background-color: #ecfdf5;
        }

        /* Sidebar Navigation */
        .sidebar-nav {
            margin-top: 16px;
        }

        .nav-item {
            width: 100%;
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            margin: 0 8px;
            border-radius: 8px;
            transition: all 0.2s ease;
            cursor: pointer;
            border: none;
            background: none;
            text-align: left;
            color: #374151;
        }

        .nav-item:hover {
            background-color: #ecfdf5;
            color: #10b981;
        }

        .nav-item.active {
            background-color: #10b981;
            color: #ffffff;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .nav-item.back-to-pos {
            color: #dc2626;
        }

        .nav-item.back-to-pos:hover {
            background-color: #fef2f2;
            color: #dc2626;
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            flex-shrink: 0;
        }

        .nav-label {
            font-weight: bold;
            font-size: 14px;
            flex: 1;
            text-align: left;
        }

        .sidebar.collapsed .nav-label {
            display: none;
        }

        .dropdown-icon {
            width: 16px;
            height: 16px;
            margin-left: auto;
        }

        .sidebar.collapsed .dropdown-icon {
            display: none;
        }

        /* Dropdown Sub-items */
        .sub-items {
            margin-left: 16px;
            margin-top: 4px;
            display: block;
        }

        .sub-items.hidden {
            display: none;
        }

        .sidebar.collapsed .sub-items {
            display: none;
        }

        .sub-item {
            width: 100%;
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px;
            margin: 0 8px 4px 8px;
            border-radius: 8px;
            transition: all 0.2s ease;
            cursor: pointer;
            border: none;
            background: none;
            text-align: left;
            color: #6b7280;
            font-size: 14px;
        }

        .sub-item:hover {
            background-color: #ecfdf5;
            color: #10b981;
        }

        .sub-item.active {
            background-color: #10b981;
            color: #ffffff;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .sub-icon {
            width: 16px;
            height: 16px;
            flex-shrink: 0;
        }

        .sub-label {
            font-weight: 500;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* Header */
        .main-header {
            background-color: #ffffff;
            border-bottom: 2px solid #e5e7eb;
            padding: 12px 16px;
            flex-shrink: 0;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            min-height: 80px;
            height: auto;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 16px;
            width: 100%;
        }

        .header-title h1 {
            font-size: 24px;
            font-weight: 900;
            color: #1f2937;
        }

        .header-title p {
            font-size: 18px;
            font-weight: bold;
            color: #10b981;
        }

        .header-info {
            text-align: right;
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 16px;
            flex-shrink: 0;
        }

        .header-info-text {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 2px;
        }

        .header-info-text div {
            font-size: 12px;
            font-weight: bold;
            color: #374151;
            white-space: nowrap;
        }

        .header-info span {
            background-color: #f3f4f6;
            padding: 2px 8px;
            font-size: 14px;
            font-weight: 900;
            color: #1f2937;
            border-radius: 4px;
            margin-left: 6px;
        }

        .header-info .admin-span {
            background-color: #ecfdf5;
            color: #065f46;
        }

        .header-controls {
            display: flex;
            gap: 4px;
            align-items: center;
            flex-shrink: 0;
        }

        .header-control-btn {
            background: #10b981;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            transition: all 0.3s ease;
            min-width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .header-control-btn.minimize {
            background: #f59e0b;
        }

        .header-control-btn.close {
            background: #ef4444;
        }

        .header-control-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        /* Content Area */
        .content-area {
            flex: 1;
            overflow-y: auto;
            background-color: #f9fafb;
            padding: 24px;
        }

        /* Icons */
        .icon {
            width: 20px;
            height: 20px;
            fill: currentColor;
        }

        .icon-small {
            width: 16px;
            height: 16px;
            fill: currentColor;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .header-content {
                flex-direction: column;
                gap: 16px;
                align-items: flex-start;
            }

            .header-info {
                width: 100%;
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
            }

            .header-info-text {
                flex-direction: row;
                gap: 16px;
                align-items: center;
            }

            .header-info-text div {
                font-size: 12px;
            }

            .header-info span {
                font-size: 14px;
                padding: 2px 8px;
            }

            .header-control-btn {
                padding: 4px 8px;
                font-size: 12px;
                min-width: 28px;
                height: 28px;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -256px;
                z-index: 1000;
                height: 100vh;
            }

            .sidebar.open {
                left: 0;
            }

            .sidebar.collapsed {
                left: -64px;
            }

            .main-content {
                width: 100%;
            }

            .main-header {
                height: auto;
                padding: 12px;
            }

            .header-info-text {
                flex-direction: column;
                gap: 4px;
                align-items: flex-start;
            }

            .header-controls {
                gap: 4px;
            }

            .header-control-btn {
                padding: 6px 8px;
                font-size: 12px;
                min-width: 30px;
                height: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <!-- Sidebar Header -->
            <div class="sidebar-header">
                <div class="sidebar-header-content">
                    <h2 class="sidebar-title">ADMIN PANEL</h2>
                    <button class="toggle-btn" onclick="toggleSidebar()">
                        <svg class="icon" id="toggle-icon" viewBox="0 0 24 24">
                            <path d="M3 12h18m-9-9l9 9-9 9"/>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Sidebar Navigation -->
            <nav class="sidebar-nav">
                <button class="nav-item" onclick="handleNavClick('dashboard')">
                    <svg class="nav-icon" viewBox="0 0 24 24">
                        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                        <polyline points="9,22 9,12 15,12 15,22"/>
                    </svg>
                    <span class="nav-label">Dashboard</span>
                </button>

                <button class="nav-item" onclick="handleNavClick('master')">
                    <svg class="nav-icon" viewBox="0 0 24 24">
                        <ellipse cx="12" cy="5" rx="9" ry="3"/>
                        <path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"/>
                        <path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"/>
                    </svg>
                    <span class="nav-label">Master</span>
                    <svg class="dropdown-icon" id="master-dropdown" viewBox="0 0 24 24">
                        <polyline points="6,9 12,15 18,9"/>
                    </svg>
                </button>

                <div class="sub-items" id="master-subitems">
                    <button class="sub-item active" onclick="handleNavClick('setup-location')">
                        <svg class="sub-icon" viewBox="0 0 24 24">
                            <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                            <circle cx="12" cy="10" r="3"/>
                        </svg>
                        <span class="sub-label">Set Up Location</span>
                    </button>
                    <button class="sub-item" onclick="handleNavClick('setup-category')">
                        <svg class="sub-icon" viewBox="0 0 24 24">
                            <path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"/>
                            <line x1="7" y1="7" x2="7.01" y2="7"/>
                        </svg>
                        <span class="sub-label">Set Up Category</span>
                    </button>
                    <button class="sub-item" onclick="handleNavClick('setup-supplier')">
                        <svg class="sub-icon" viewBox="0 0 24 24">
                            <rect x="1" y="3" width="15" height="13"/>
                            <polygon points="16,8 20,8 23,11 23,16 16,16"/>
                            <circle cx="5.5" cy="18.5" r="2.5"/>
                            <circle cx="18.5" cy="18.5" r="2.5"/>
                        </svg>
                        <span class="sub-label">Set Up Supplier</span>
                    </button>
                    <button class="sub-item" onclick="handleNavClick('setup-product')">
                        <svg class="sub-icon" viewBox="0 0 24 24">
                            <circle cx="9" cy="21" r="1"/>
                            <circle cx="20" cy="21" r="1"/>
                            <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"/>
                        </svg>
                        <span class="sub-label">Set Up Product</span>
                    </button>
                </div>

                <button class="nav-item" onclick="handleNavClick('reports')">
                    <svg class="nav-icon" viewBox="0 0 24 24">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                        <polyline points="14,2 14,8 20,8"/>
                        <line x1="16" y1="13" x2="8" y2="13"/>
                        <line x1="16" y1="17" x2="8" y2="17"/>
                        <polyline points="10,9 9,9 8,9"/>
                    </svg>
                    <span class="nav-label">Reports</span>
                </button>

                <button class="nav-item" onclick="handleNavClick('transactions')">
                    <svg class="nav-icon" viewBox="0 0 24 24">
                        <rect x="1" y="4" width="22" height="16" rx="2" ry="2"/>
                        <line x1="1" y1="10" x2="23" y2="10"/>
                    </svg>
                    <span class="nav-label">Transactions</span>
                </button>

                <button class="nav-item" onclick="handleNavClick('wholesale')">
                    <svg class="nav-icon" viewBox="0 0 24 24">
                        <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
                        <polyline points="3.27,6.96 12,12.01 20.73,6.96"/>
                        <line x1="12" y1="22.08" x2="12" y2="12"/>
                    </svg>
                    <span class="nav-label">Wholesale</span>
                </button>

                <button class="nav-item" onclick="handleNavClick('user-management')">
                    <svg class="nav-icon" viewBox="0 0 24 24">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                        <circle cx="12" cy="7" r="4"/>
                    </svg>
                    <span class="nav-label">User Management</span>
                </button>

                <button class="nav-item back-to-pos" onclick="handleNavClick('back-to-pos')">
                    <svg class="nav-icon" viewBox="0 0 24 24">
                        <line x1="19" y1="12" x2="5" y2="12"/>
                        <polyline points="12,19 5,12 12,5"/>
                    </svg>
                    <span class="nav-label">Back To POS</span>
                </button>
            </nav>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <!-- Header -->
            <div class="main-header">
                <div class="header-content">
                    <div class="header-title">
                        <h1>ADMIN DASHBOARD</h1>
                        <p>Rainbow Station Inc.</p>
                    </div>
                    <div class="header-info">
                        <div class="header-info-text">
                            <div>Date: <span id="current-date">01/01/24</span></div>
                            <div>Time: <span id="current-time">00:00:00</span></div>
                            <div>Admin: <span class="admin-span">Simon</span></div>
                        </div>
                        <div class="header-controls">
                            <button onclick="toggleFullscreen()" id="fullscreen-btn" class="header-control-btn" title="Maximize Window">⛶</button>
                            <button onclick="minimizeApp()" class="header-control-btn minimize" title="Minimize">−</button>
                            <button onclick="closeApp()" class="header-control-btn close" title="Close">×</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Area -->
            <div class="content-area" id="content-area">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>

    <script src="admin.js"></script>
</body>
</html>
