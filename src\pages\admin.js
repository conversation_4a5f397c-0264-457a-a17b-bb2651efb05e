const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// Global state
let isCollapsed = true;
let activeItem = "dashboard"; // Will be updated based on user permissions
let isMasterExpanded = true; // Master dropdown expanded by default

// Global function to recover input functionality
window.recoverInputs = function() {
    console.log('Manually recovering input functionality...');
    ensureInputsAreFunctional();

    // Remove any readonly attributes that might be blocking inputs
    const inputs = document.querySelectorAll('input[readonly], select[readonly], textarea[readonly]');
    inputs.forEach(input => {
        if (!input.hasAttribute('data-should-be-disabled')) {
            input.removeAttribute('readonly');
        }
    });

    alert('Input functionality recovered! Try clicking the input fields now.');
};

// Initialize the application
document.addEventListener('DOMContentLoaded', async function() {
    updateTime();
    setInterval(updateTime, 1000);
    await loadCurrentUser(); // Load current user info
    renderMainContent();
    updateActiveNavigation();

    // Set up periodic input recovery
    setInterval(() => {
        ensureInputsAreFunctional();
    }, 5000); // Check every 5 seconds

    // Initialize maximize button state
    try {
        const isMaximized = await ipcRenderer.invoke('is-fullscreen');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = isMaximized ? '🗗' : '⛶';
            fullscreenBtn.title = isMaximized ? 'Restore Window' : 'Maximize Window';
        }
    } catch (error) {
        console.error('Error checking initial maximize state:', error);
    }
});

// Current user management
let currentUser = null;
let userPermissions = [];

async function loadCurrentUser() {
    try {
        currentUser = await ipcRenderer.invoke('get-current-user');
        userPermissions = currentUser.permissions || [];

        console.log('=== USER LOGIN DEBUG ===');
        console.log('Current user:', currentUser);
        console.log('User role:', currentUser.role);
        console.log('User permissions count:', userPermissions.length);
        console.log('User permissions:', userPermissions);

        // Log each permission in detail
        userPermissions.forEach((perm, index) => {
            console.log(`Permission ${index + 1}:`, {
                module_id: perm.module_id,
                module_name: perm.module_name,
                can_view: perm.can_view,
                can_edit: perm.can_edit,
                can_delete: perm.can_delete
            });
        });
        console.log('=== END DEBUG ===');

        updateHeaderUserInfo();

        // Apply permission-based restrictions
        applyPermissionRestrictions();
    } catch (error) {
        console.error('Error loading current user:', error);
    }
}

// Permission checking utilities
function hasModuleAccess(moduleId) {
    // Admin has access to everything
    if (currentUser && currentUser.role === 'Admin') {
        return true;
    }

    // Check if user has permission for this module
    const hasAccess = userPermissions.some(perm => perm.module_id === moduleId);
    console.log(`Checking access for module '${moduleId}': ${hasAccess}`);

    if (!hasAccess) {
        console.log('Available permissions:', userPermissions.map(p => p.module_id));
    }

    return hasAccess;
}

function hasPermission(moduleId, operation) {
    // Admin has all permissions
    if (currentUser && currentUser.role === 'Admin') {
        return true;
    }

    const permission = userPermissions.find(perm => perm.module_id === moduleId);
    if (!permission) return false;

    switch (operation) {
        case 'view':
            return permission.can_view === 1;
        case 'edit':
            return permission.can_edit === 1;
        case 'delete':
            return permission.can_delete === 1;
        default:
            return false;
    }
}

function getFirstAvailableModule() {
    // For Admin users, always default to dashboard
    if (currentUser && currentUser.role === 'Admin') {
        return 'dashboard';
    }

    // For non-admin users, check permissions in order of preference
    const moduleOrder = [
        'dashboard',           // First preference: Dashboard
        'master',             // Second: Master Data
        'setup-location',     // Master sub-items
        'setup-category',
        'setup-supplier',
        'setup-product',
        'reports',            // Other admin modules
        'transactions',
        'wholesale',
        'user-management'
    ];

    for (const moduleId of moduleOrder) {
        if (hasModuleAccess(moduleId)) {
            console.log(`First available module for user: ${moduleId}`);
            return moduleId;
        }
    }

    // If no modules found, return dashboard (will show access denied)
    console.log('No available modules found, defaulting to dashboard');
    return 'dashboard';
}

function filterSidebarNavigation() {
    // Get all navigation items
    const navItems = document.querySelectorAll('.nav-item');

    navItems.forEach(item => {
        const itemId = item.getAttribute('onclick')?.match(/handleNavClick\('([^']+)'\)/)?.[1];

        if (itemId) {
            // Always show back-to-pos
            if (itemId === 'back-to-pos') {
                item.style.display = 'block';
                return;
            }

            // Check if user has access to this module
            if (hasModuleAccess(itemId)) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        }
    });

    // Handle master dropdown items
    filterMasterDropdownItems();
}

function filterMasterDropdownItems() {
    const masterSubItems = ['setup-location', 'setup-category', 'setup-supplier', 'setup-product'];
    const masterDropdown = document.getElementById('master-dropdown-items');

    if (masterDropdown) {
        const subItems = masterDropdown.querySelectorAll('.nav-sub-item');
        subItems.forEach(item => {
            const itemId = item.getAttribute('onclick')?.match(/handleNavClick\('([^']+)'\)/)?.[1];

            if (itemId && masterSubItems.includes(itemId)) {
                if (hasModuleAccess(itemId)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            }
        });
    }
}

function applyPermissionRestrictions() {
    // Filter sidebar navigation
    filterSidebarNavigation();

    // For non-admin users, set activeItem to first available module
    if (currentUser && currentUser.role !== 'Admin') {
        const firstAvailableModule = getFirstAvailableModule();
        if (firstAvailableModule) {
            activeItem = firstAvailableModule;
        }
    }

    // Update current page if user doesn't have access
    if (!hasModuleAccess(activeItem)) {
        // Redirect to first available module
        const firstAvailableModule = getFirstAvailableModule();
        if (firstAvailableModule) {
            activeItem = firstAvailableModule;
        }
    }

    // Render the content and update navigation
    renderMainContent();
    updateActiveNavigation();
}

function updateHeaderUserInfo() {
    if (currentUser) {
        const adminSpan = document.querySelector('.admin-span');
        if (adminSpan) {
            adminSpan.textContent = currentUser.name || currentUser.username;
        }

        // Update role display if needed
        const roleElement = document.getElementById('user-role');
        if (roleElement) {
            roleElement.textContent = currentUser.role;
        }
    }
}

// Time functions
function updateTime() {
    const now = new Date();
    document.getElementById('current-date').textContent = formatDate(now);
    document.getElementById('current-time').textContent = formatTime(now);
}

function formatDate(date) {
    return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit"
    });
}

function formatTime(date) {
    return date.toLocaleTimeString("en-US", { hour12: false });
}

// Sidebar functions
function toggleSidebar() {
    isCollapsed = !isCollapsed;
    const sidebar = document.getElementById('sidebar');
    const toggleIcon = document.getElementById('toggle-icon');

    if (isCollapsed) {
        sidebar.classList.add('collapsed');
        toggleIcon.innerHTML = '<path d="M3 12h18m-9-9l9 9-9 9"/>';
    } else {
        sidebar.classList.remove('collapsed');
        toggleIcon.innerHTML = '<path d="M6 18L18 6M6 6l12 12"/>';
    }
}

// Navigation functions
function handleNavClick(itemId) {
    if (itemId === 'back-to-pos') {
        ipcRenderer.invoke('navigate-to', 'pos');
        return;
    }

    if (itemId === 'master') {
        isMasterExpanded = !isMasterExpanded;
        updateMasterDropdown();
        return;
    }

    // Check if user has access to this module
    if (!hasModuleAccess(itemId)) {
        alert('Access denied. You do not have permission to access this module.');
        return;
    }

    activeItem = itemId;

    // If clicking a master sub-item, ensure master is expanded
    const masterSubItems = ['setup-location', 'setup-category', 'setup-supplier', 'setup-product'];
    if (masterSubItems.includes(itemId)) {
        isMasterExpanded = true;
        updateMasterDropdown();
    }

    renderMainContent();
    updateActiveNavigation();
}

// Window control functions
function closeApp() {
    ipcRenderer.invoke('close-app');
}

function minimizeApp() {
    ipcRenderer.invoke('minimize-app');
}

async function toggleFullscreen() {
    try {
        const isMaximized = await ipcRenderer.invoke('toggle-fullscreen');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = isMaximized ? '🗗' : '⛶';
            fullscreenBtn.title = isMaximized ? 'Restore Window' : 'Maximize Window';
        }
    } catch (error) {
        console.error('Error toggling maximize:', error);
    }
}

function updateMasterDropdown() {
    const subItems = document.getElementById('master-subitems');
    const dropdownIcon = document.getElementById('master-dropdown');

    if (isMasterExpanded) {
        subItems.classList.remove('hidden');
        dropdownIcon.innerHTML = '<polyline points="6,9 12,15 18,9"/>';
    } else {
        subItems.classList.add('hidden');
        dropdownIcon.innerHTML = '<polyline points="9,6 15,12 9,18"/>';
    }
}

function updateActiveNavigation() {
    // Remove active class from all nav items
    document.querySelectorAll('.nav-item, .sub-item').forEach(item => {
        item.classList.remove('active');
    });

    // Add active class to current item
    const masterSubItems = ['setup-location', 'setup-category', 'setup-supplier', 'setup-product'];

    if (masterSubItems.includes(activeItem)) {
        // Activate the specific sub-item
        document.querySelectorAll('.sub-item').forEach(item => {
            if (item.onclick && item.onclick.toString().includes(activeItem)) {
                item.classList.add('active');
            }
        });
    } else {
        // Activate main nav item
        document.querySelectorAll('.nav-item').forEach(item => {
            if (item.onclick && item.onclick.toString().includes(activeItem)) {
                item.classList.add('active');
            }
        });
    }
}

// Content rendering functions
function renderMainContent() {
    const contentArea = document.getElementById('content-area');

    // Check if user has access to current module
    if (!hasModuleAccess(activeItem)) {
        contentArea.innerHTML = renderAccessDenied();
        return;
    }

    switch (activeItem) {
        case "dashboard":
            contentArea.innerHTML = renderDashboard();
            break;
        case "master":
            contentArea.innerHTML = renderMaster();
            break;
        case "setup-location":
            contentArea.innerHTML = renderSetUpLocation();
            break;
        case "setup-category":
            contentArea.innerHTML = renderSetUpCategory();
            break;
        case "setup-supplier":
            contentArea.innerHTML = renderSetUpSupplier();
            break;
        case "setup-product":
            contentArea.innerHTML = renderSetUpProduct();
            break;
        case "reports":
            contentArea.innerHTML = renderReports();
            break;
        case "transactions":
            contentArea.innerHTML = renderTransactions();
            break;
        case "wholesale":
            contentArea.innerHTML = renderWholesale();
            break;
        case "user-management":
            contentArea.innerHTML = renderUserManagement();
            break;
        default:
            contentArea.innerHTML = renderDashboard(); // Default to dashboard
    }

    // Ensure inputs are functional immediately after rendering
    setTimeout(() => {
        ensureInputsAreFunctional();
        applyContentPermissions();

        // Additional check after a longer delay
        setTimeout(() => {
            ensureInputsAreFunctional();
        }, 200);
    }, 50); // Reduced delay for faster response
}

function renderAccessDenied() {
    return `
        <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 400px; text-align: center;">
            <div style="font-size: 64px; margin-bottom: 24px;">🚫</div>
            <h2 style="color: #dc2626; font-size: 24px; margin-bottom: 16px;">Access Denied</h2>
            <p style="color: #6b7280; font-size: 16px; margin-bottom: 24px;">You do not have permission to access this module.</p>
            <p style="color: #6b7280; font-size: 14px;">Contact your administrator to request access.</p>
        </div>
    `;
}

function applyContentPermissions() {
    console.log('Applying content permissions for module:', activeItem);

    // Always ensure inputs are functional first
    ensureInputsAreFunctional();

    // Only apply restrictions for non-admin users
    if (currentUser && currentUser.role !== 'Admin') {
        // Hide edit/delete buttons if user only has view permission
        if (!hasPermission(activeItem, 'edit')) {
            hideEditButtons();
        }

        if (!hasPermission(activeItem, 'delete')) {
            hideDeleteButtons();
        }

        // Make forms read-only if user only has view permission
        if (!hasPermission(activeItem, 'edit')) {
            makeFormsReadOnly();
        }
    }

    // Final check to ensure inputs are still functional
    setTimeout(() => {
        ensureInputsAreFunctional();
    }, 50);
}

// Ensure all input fields are functional and clickable
function ensureInputsAreFunctional() {
    console.log('Ensuring inputs are functional...');

    // Get all input elements
    const inputs = document.querySelectorAll('input, select, textarea');

    inputs.forEach(input => {
        // Remove any blocking styles or attributes
        input.style.pointerEvents = 'auto';
        input.style.userSelect = 'auto';
        input.style.webkitUserSelect = 'auto';
        input.style.mozUserSelect = 'auto';
        input.style.msUserSelect = 'auto';
        input.style.cursor = 'text';
        input.style.opacity = '1';
        input.style.zIndex = 'auto';

        // Ensure input is not disabled unless it should be
        if (!input.hasAttribute('data-should-be-disabled')) {
            input.disabled = false;
            input.removeAttribute('readonly');
        }

        // Add focus and click event listeners if they don't exist
        if (!input.hasAttribute('data-events-added')) {
            input.addEventListener('focus', function() {
                console.log('Input focused:', this.id || this.name || 'unnamed');
                this.style.outline = '2px solid #3b82f6';
            });

            input.addEventListener('blur', function() {
                this.style.outline = '';
            });

            input.addEventListener('click', function(e) {
                console.log('Input clicked:', this.id || this.name || 'unnamed');
                e.stopPropagation();
                this.focus();
            });

            input.setAttribute('data-events-added', 'true');
        }
    });

    // Ensure form containers are clickable
    const forms = document.querySelectorAll('form, .form-container');
    forms.forEach(form => {
        form.style.pointerEvents = 'auto';
        form.style.userSelect = 'auto';
    });

    console.log(`Ensured ${inputs.length} inputs are functional`);
}

function hideEditButtons() {
    const editButtons = document.querySelectorAll('button[onclick*="edit"], .edit-btn, [onclick*="Edit"]');
    editButtons.forEach(button => {
        if (!button.textContent.includes('Add') && !button.textContent.includes('New')) {
            button.style.display = 'none';
        }
    });
}

function hideDeleteButtons() {
    const deleteButtons = document.querySelectorAll('button[onclick*="delete"], .delete-btn, [onclick*="Delete"]');
    deleteButtons.forEach(button => {
        button.style.display = 'none';
    });
}

function makeFormsReadOnly() {
    console.log('Making forms read-only for view-only access');

    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        if (input.type !== 'button' && input.type !== 'submit') {
            // Use readonly instead of disabled to maintain functionality
            input.setAttribute('readonly', 'true');
            input.setAttribute('data-should-be-disabled', 'true');
            input.style.backgroundColor = '#f9fafb';
            input.style.cursor = 'default';
            input.style.pointerEvents = 'auto'; // Keep clickable for focus

            // Allow focus but prevent editing
            input.addEventListener('keydown', function(e) {
                if (this.hasAttribute('readonly')) {
                    e.preventDefault();
                }
            });

            input.addEventListener('paste', function(e) {
                if (this.hasAttribute('readonly')) {
                    e.preventDefault();
                }
            });
        }
    });

    // Hide submit buttons for read-only forms
    const submitButtons = document.querySelectorAll('button[type="submit"], .submit-btn');
    submitButtons.forEach(button => {
        button.style.display = 'none';
    });

    // Add read-only indicator
    const contentArea = document.getElementById('content-area');
    if (contentArea && !contentArea.querySelector('.readonly-indicator')) {
        const indicator = document.createElement('div');
        indicator.className = 'readonly-indicator';
        indicator.style.cssText = `
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
            color: #92400e;
            font-size: 14px;
            font-weight: 500;
        `;
        indicator.innerHTML = '👁️ View-only mode: You can view data but cannot make changes.';
        contentArea.insertBefore(indicator, contentArea.firstChild);
    }
}

function renderDashboard() {
    return `
        <div>
            <h2 style="font-size: 32px; font-weight: 900; color: #1f2937; margin-bottom: 24px;">Dashboard Overview</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 24px; margin-bottom: 32px;">
                <div style="background-color: #ffffff; border: 2px solid #10b981; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                    <h3 style="font-size: 18px; font-weight: bold; color: #10b981; margin-bottom: 8px;">Today's Sales</h3>
                    <p style="font-size: 32px; font-weight: 900; color: #1f2937;">$2,450.75</p>
                </div>
                <div style="background-color: #ffffff; border: 2px solid #3b82f6; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                    <h3 style="font-size: 18px; font-weight: bold; color: #3b82f6; margin-bottom: 8px;">Transactions</h3>
                    <p style="font-size: 32px; font-weight: 900; color: #1f2937;">127</p>
                </div>
                <div style="background-color: #ffffff; border: 2px solid #f59e0b; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                    <h3 style="font-size: 18px; font-weight: bold; color: #f59e0b; margin-bottom: 8px;">Products Sold</h3>
                    <p style="font-size: 32px; font-weight: 900; color: #1f2937;">342</p>
                </div>
                <div style="background-color: #ffffff; border: 2px solid #ef4444; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                    <h3 style="font-size: 18px; font-weight: bold; color: #ef4444; margin-bottom: 8px;">Active Users</h3>
                    <p style="font-size: 32px; font-weight: 900; color: #1f2937;">8</p>
                </div>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 24px; margin-bottom: 32px;">
                <div style="background-color: #ffffff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                    <h3 style="font-size: 20px; font-weight: bold; color: #1f2937; margin-bottom: 16px;">Recent Activity</h3>
                    <div style="display: flex; flex-direction: column; gap: 12px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background-color: #ecfdf5; border: 1px solid #d1fae5; border-radius: 4px;">
                            <span style="color: #065f46; font-weight: 500;">Sale completed - $45.99</span>
                            <span style="color: #6b7280; font-size: 14px;">2 minutes ago</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background-color: #eff6ff; border: 1px solid #dbeafe; border-radius: 4px;">
                            <span style="color: #1e40af; font-weight: 500;">New user registered - John Doe</span>
                            <span style="color: #6b7280; font-size: 14px;">5 minutes ago</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background-color: #fffbeb; border: 1px solid #fed7aa; border-radius: 4px;">
                            <span style="color: #92400e; font-weight: 500;">Inventory updated - 25 items</span>
                            <span style="color: #6b7280; font-size: 14px;">10 minutes ago</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background-color: #faf5ff; border: 1px solid #e9d5ff; border-radius: 4px;">
                            <span style="color: #7c2d12; font-weight: 500;">Report generated - Monthly Sales</span>
                            <span style="color: #6b7280; font-size: 14px;">15 minutes ago</span>
                        </div>
                    </div>
                </div>

                <div style="background-color: #ffffff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                    <h3 style="font-size: 20px; font-weight: bold; color: #1f2937; margin-bottom: 16px;">Quick Actions</h3>
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px;">
                        <button style="background-color: #10b981; color: #ffffff; font-weight: bold; padding: 12px 16px; border-radius: 8px; transition: background-color 0.3s ease; border: none; cursor: pointer;" onmouseover="this.style.backgroundColor='#059669'" onmouseout="this.style.backgroundColor='#10b981'">
                            Add Product
                        </button>
                        <button style="background-color: #3b82f6; color: #ffffff; font-weight: bold; padding: 12px 16px; border-radius: 8px; transition: background-color 0.3s ease; border: none; cursor: pointer;" onmouseover="this.style.backgroundColor='#2563eb'" onmouseout="this.style.backgroundColor='#3b82f6'">
                            View Reports
                        </button>
                        <button style="background-color: #f59e0b; color: #ffffff; font-weight: bold; padding: 12px 16px; border-radius: 8px; transition: background-color 0.3s ease; border: none; cursor: pointer;" onmouseover="this.style.backgroundColor='#d97706'" onmouseout="this.style.backgroundColor='#f59e0b'">
                            Manage Users
                        </button>
                        <button style="background-color: #8b5cf6; color: #ffffff; font-weight: bold; padding: 12px 16px; border-radius: 8px; transition: background-color 0.3s ease; border: none; cursor: pointer;" onmouseover="this.style.backgroundColor='#7c3aed'" onmouseout="this.style.backgroundColor='#8b5cf6'">
                            Settings
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function renderMaster() {
    return `
        <div>
            <h2 style="font-size: 32px; font-weight: 900; color: #1f2937; margin-bottom: 24px;">Master Data Management</h2>
            <p style="font-size: 18px; color: #6b7280; margin-bottom: 32px;">Select a master data category from the sidebar to manage your system data.</p>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 24px;">
                <div style="background-color: #ffffff; border: 2px solid #10b981; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); cursor: pointer;" onclick="handleNavClick('setup-location')">
                    <h3 style="font-size: 20px; font-weight: bold; color: #10b981; margin-bottom: 12px;">Set Up Location</h3>
                    <p style="color: #6b7280;">Manage store locations and branches</p>
                </div>
                <div style="background-color: #ffffff; border: 2px solid #3b82f6; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); cursor: pointer;" onclick="handleNavClick('setup-category')">
                    <h3 style="font-size: 20px; font-weight: bold; color: #3b82f6; margin-bottom: 12px;">Set Up Category</h3>
                    <p style="color: #6b7280;">Organize products into categories</p>
                </div>
                <div style="background-color: #ffffff; border: 2px solid #f59e0b; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); cursor: pointer;" onclick="handleNavClick('setup-supplier')">
                    <h3 style="font-size: 20px; font-weight: bold; color: #f59e0b; margin-bottom: 12px;">Set Up Supplier</h3>
                    <p style="color: #6b7280;">Manage supplier information</p>
                </div>
                <div style="background-color: #ffffff; border: 2px solid #ef4444; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); cursor: pointer;" onclick="handleNavClick('setup-product')">
                    <h3 style="font-size: 20px; font-weight: bold; color: #ef4444; margin-bottom: 12px;">Set Up Product</h3>
                    <p style="color: #6b7280;">Add and manage product inventory</p>
                </div>
            </div>
        </div>
    `;
}

function renderSetUpLocation() {
    setTimeout(() => {
        // Initialize location functionality after DOM is loaded
        initializeLocationPage();
    }, 100);

    return `
        <div style="padding: 24px;">
            <!-- Action Buttons -->
            <div style="display: flex; gap: 12px; margin-bottom: 24px;">
                <button onclick="saveLocation()" style="background-color: #059669; color: white; border: none; border-radius: 4px; padding: 8px 32px; font-size: 14px; font-weight: bold; cursor: pointer;">SAVE</button>
                <button onclick="clearLocationForm()" style="background-color: #2563eb; color: white; border: none; border-radius: 4px; padding: 8px 32px; font-size: 14px; font-weight: bold; cursor: pointer;">CLEAR</button>
                <button onclick="deleteLocation()" style="background-color: #dc2626; color: white; border: none; border-radius: 4px; padding: 8px 32px; font-size: 14px; font-weight: bold; cursor: pointer;">DELETE</button>
            </div>

            <!-- Main Form -->
            <div style="background-color: #ffffff; border: 1px solid #d1d5db; border-radius: 8px; box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); padding: 24px; margin-bottom: 32px;">
                <div style="display: grid; gap: 24px;">
                    <!-- Location Code and Location Row -->
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px;">
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Location Code</label>
                            <input type="text" id="locationCode" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="Enter location code">
                        </div>
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Location</label>
                            <input type="text" id="location" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="Enter location name">
                        </div>
                    </div>

                    <!-- Company Name and E-mail Row -->
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px;">
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Company Name</label>
                            <input type="text" id="companyName" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="Enter company name">
                        </div>
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">E-mail</label>
                            <input type="email" id="email" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="Enter email address">
                        </div>
                    </div>

                    <!-- Address1 and App Mode Row -->
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px;">
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Address1</label>
                            <input type="text" id="address1" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="Enter address line 1">
                        </div>
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">App Mode</label>
                            <select id="appMode" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">
                                <option value="">Select App Mode</option>
                                <option value="retail">Retail</option>
                                <option value="wholesale">Wholesale</option>
                                <option value="both">Both</option>
                            </select>
                        </div>
                    </div>

                    <!-- Address2 and Theater PLU Row -->
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px;">
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Address2</label>
                            <input type="text" id="address2" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="Enter address line 2">
                        </div>
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Theater PLU</label>
                            <input type="text" id="theaterPLU" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="Enter theater PLU">
                        </div>
                    </div>

                    <!-- Phone and Theater Time Row -->
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px;">
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Phone</label>
                            <input type="tel" id="phone" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="Enter phone number">
                        </div>
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Theater Time</label>
                            <input type="text" id="theaterTime" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="Enter theater time">
                        </div>
                    </div>

                    <!-- Tax% and Deli Row -->
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px;">
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Tax%</label>
                            <input type="number" step="0.001" id="taxPercent" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="Enter tax percentage">
                        </div>
                        <div style="display: flex; align-items: center; gap: 8px; padding-top: 32px;">
                            <input type="checkbox" id="deli" style="width: 16px; height: 16px;">
                            <label for="deli" style="font-size: 14px; font-weight: bold; color: #374151;">Deli</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Location Directory Table -->
            <div style="background-color: #ffffff; border: 1px solid #d1d5db; border-radius: 8px; box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); padding: 24px;">
                <div style="border: 1px solid #d1d5db; border-radius: 8px; overflow: hidden;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background-color: #2563eb; color: white;">
                                <th style="text-align: left; padding: 12px; font-weight: bold; border-right: 1px solid #1d4ed8;">Location Code</th>
                                <th style="text-align: left; padding: 12px; font-weight: bold; border-right: 1px solid #1d4ed8;">Location</th>
                                <th style="text-align: left; padding: 12px; font-weight: bold; border-right: 1px solid #1d4ed8;">Company Name</th>
                                <th style="text-align: left; padding: 12px; font-weight: bold; border-right: 1px solid #1d4ed8;">Address1</th>
                                <th style="text-align: left; padding: 12px; font-weight: bold; border-right: 1px solid #1d4ed8;">Address2</th>
                                <th style="text-align: center; padding: 12px; font-weight: bold; border-right: 1px solid #1d4ed8;">Phone#</th>
                                <th style="text-align: center; padding: 12px; font-weight: bold;">Tax%</th>
                            </tr>
                        </thead>
                        <tbody id="locationTableBody">
                            <!-- Table rows will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;
}

function renderSetUpCategory() {
    setTimeout(() => {
        // Initialize category functionality after DOM is loaded
        initializeCategoryPage();
    }, 100);

    return `
        <div style="padding: 24px;">
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 24px; margin-bottom: 32px;">
                <!-- Add New Category Card -->
                <div style="background-color: #ffffff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                    <h3 style="font-size: 20px; font-weight: bold; color: #059669; margin-bottom: 16px;">Add New Category</h3>
                    <div style="display: flex; flex-direction: column; gap: 16px;">
                        <div>
                            <label style="display: block; font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px;">Category Name</label>
                            <input type="text" id="categoryName" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;" placeholder="Enter category name">
                        </div>
                        <div>
                            <label style="display: block; font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px;">Category Code</label>
                            <input type="text" id="categoryCode" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;" placeholder="Enter category code">
                        </div>
                        <div>
                            <label style="display: block; font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px;">Parent Category</label>
                            <select id="parentCategory" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">
                                <option value="">Select Parent Category (Optional)</option>
                                <option value="A">A. Pipes</option>
                                <option value="B">B. Nailpolish Remo</option>
                                <option value="C">C. Miscellaneous</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px;">Description</label>
                            <textarea id="categoryDescription" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; resize: vertical; min-height: 80px;" placeholder="Enter category description"></textarea>
                        </div>
                        <div>
                            <label style="display: block; font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px;">Status</label>
                            <select id="categoryStatus" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                        <button onclick="addCategory()" style="background-color: #059669; color: white; border: none; border-radius: 8px; padding: 12px 24px; font-size: 14px; font-weight: bold; cursor: pointer; width: 100%;">Add Category</button>
                    </div>
                </div>

                <!-- Category Hierarchy Card -->
                <div style="background-color: #ffffff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                    <h3 style="font-size: 20px; font-weight: bold; color: #2563eb; margin-bottom: 16px;">Category Hierarchy</h3>
                    <div id="categoryHierarchy" style="display: flex; flex-direction: column; gap: 12px;">
                        <!-- Hierarchy items will be populated by JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Category Management Table -->
            <div style="background-color: #ffffff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                    <h3 style="font-size: 20px; font-weight: bold; color: #1f2937;">Category Management</h3>
                    <div style="display: flex; gap: 8px;">
                        <button onclick="importCategories()" style="background-color: #2563eb; color: white; border: none; border-radius: 4px; padding: 8px 16px; font-size: 14px; font-weight: bold; cursor: pointer;">Import Categories</button>
                        <button onclick="exportCategories()" style="background-color: #059669; color: white; border: none; border-radius: 4px; padding: 8px 16px; font-size: 14px; font-weight: bold; cursor: pointer;">Export Categories</button>
                    </div>
                </div>

                <div style="overflow-x: auto;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background-color: #f9fafb; border-bottom: 2px solid #e5e7eb;">
                                <th style="padding: 12px; text-align: left; font-weight: bold; color: #374151;">Code</th>
                                <th style="padding: 12px; text-align: left; font-weight: bold; color: #374151;">Category Name</th>
                                <th style="padding: 12px; text-align: left; font-weight: bold; color: #374151;">Parent Category</th>
                                <th style="padding: 12px; text-align: left; font-weight: bold; color: #374151;">Products Count</th>
                                <th style="padding: 12px; text-align: left; font-weight: bold; color: #374151;">Status</th>
                                <th style="padding: 12px; text-align: left; font-weight: bold; color: #374151;">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="categoryTableBody">
                            <!-- Table rows will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;
}

function renderSetUpSupplier() {
    setTimeout(() => {
        // Initialize supplier functionality after DOM is loaded
        initializeSupplierPage();
    }, 100);

    return `
        <div style="padding: 24px;">
            <!-- Action Buttons -->
            <div style="display: flex; gap: 12px; margin-bottom: 24px;">
                <button onclick="saveSupplier()" style="background-color: #059669; color: white; border: none; border-radius: 4px; padding: 8px 32px; font-size: 14px; font-weight: bold; cursor: pointer;">SAVE</button>
                <button onclick="clearSupplierForm()" style="background-color: #2563eb; color: white; border: none; border-radius: 4px; padding: 8px 32px; font-size: 14px; font-weight: bold; cursor: pointer;">CLEAR</button>
                <button onclick="deleteSupplier()" style="background-color: #dc2626; color: white; border: none; border-radius: 4px; padding: 8px 32px; font-size: 14px; font-weight: bold; cursor: pointer;">DELETE</button>
            </div>

            <!-- Main Form -->
            <div style="background-color: #ffffff; border: 1px solid #d1d5db; border-radius: 8px; box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); padding: 24px; margin-bottom: 32px;">
                <div style="display: grid; gap: 24px;">
                    <!-- Name Row -->
                    <div>
                        <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Name</label>
                        <input type="text" id="supplierName" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="Enter supplier name">
                    </div>

                    <!-- Address Fields -->
                    <div>
                        <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Address1</label>
                        <input type="text" id="supplierAddress1" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="Enter address line 1">
                    </div>
                    <div>
                        <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Address2</label>
                        <input type="text" id="supplierAddress2" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="Enter address line 2 (optional)">
                    </div>

                    <!-- City, State, ZIP Row -->
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 16px;">
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">City</label>
                            <input type="text" id="supplierCity" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="Enter city">
                        </div>
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">State</label>
                            <input type="text" id="supplierState" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="Enter state">
                        </div>
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">ZIP Code</label>
                            <input type="text" id="supplierZipCode" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="Enter ZIP code">
                        </div>
                    </div>

                    <!-- Contact Information Row -->
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 16px;">
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Telephone</label>
                            <input type="tel" id="supplierTelephone" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="Enter telephone">
                        </div>
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Fax</label>
                            <input type="tel" id="supplierFax" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="Enter fax number">
                        </div>
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Email</label>
                            <input type="email" id="supplierEmail" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="Enter email address">
                        </div>
                    </div>

                    <!-- Sales Rep Information Row -->
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px;">
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Sale's Rep</label>
                            <input type="text" id="supplierSalesRep" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="Enter sales representative name">
                        </div>
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Sale's Rep Phone</label>
                            <input type="tel" id="supplierSalesRepPhone" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="Enter sales rep phone">
                        </div>
                    </div>

                    <!-- Website Information Row -->
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px;">
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Retail Website</label>
                            <input type="url" id="supplierRetailWebsite" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="Enter retail website URL">
                        </div>
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Wholesale Website</label>
                            <input type="url" id="supplierWholesaleWebsite" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="Enter wholesale website URL">
                        </div>
                    </div>

                    <!-- Login Credentials Row -->
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px;">
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Username</label>
                            <input type="text" id="supplierUsername" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="Enter username">
                        </div>
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Password</label>
                            <input type="password" id="supplierPassword" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="Enter password">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Supplier Directory Table -->
            <div style="background-color: #ffffff; border: 1px solid #d1d5db; border-radius: 8px; box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); padding: 24px;">
                <div style="border: 1px solid #d1d5db; border-radius: 8px; overflow: hidden;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background-color: #2563eb; color: white;">
                                <th style="text-align: left; padding: 12px; font-weight: bold; border-right: 1px solid #1d4ed8;">Supplier Name</th>
                                <th style="text-align: center; padding: 12px; font-weight: bold; border-right: 1px solid #1d4ed8;">Telephone</th>
                                <th style="text-align: center; padding: 12px; font-weight: bold; border-right: 1px solid #1d4ed8;">Fax</th>
                                <th style="text-align: center; padding: 12px; font-weight: bold; border-right: 1px solid #1d4ed8;">Email</th>
                                <th style="text-align: center; padding: 12px; font-weight: bold; border-right: 1px solid #1d4ed8;">Sale's Rep</th>
                                <th style="text-align: center; padding: 12px; font-weight: bold;">Rep Phone #</th>
                            </tr>
                        </thead>
                        <tbody id="supplierTableBody">
                            <!-- Table rows will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;
}

function renderSetUpProduct() {
    setTimeout(() => {
        // Initialize product functionality after DOM is loaded
        initializeProductPage();
    }, 100);

    return `
        <div style="padding: 24px;">
            <!-- Action Buttons -->
            <div style="display: flex; gap: 12px; margin-bottom: 24px;">
                <button onclick="saveProduct()" style="background-color: #059669; color: white; border: none; border-radius: 4px; padding: 8px 32px; font-size: 14px; font-weight: bold; cursor: pointer;">SAVE</button>
                <button onclick="clearProductForm()" style="background-color: #2563eb; color: white; border: none; border-radius: 4px; padding: 8px 32px; font-size: 14px; font-weight: bold; cursor: pointer;">CLEAR</button>
                <button onclick="deleteProduct()" style="background-color: #dc2626; color: white; border: none; border-radius: 4px; padding: 8px 32px; font-size: 14px; font-weight: bold; cursor: pointer;">DELETE</button>
            </div>

            <!-- Main Form -->
            <div style="background-color: #ffffff; border: 1px solid #d1d5db; border-radius: 8px; box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); padding: 24px; margin-bottom: 32px;">
                <div style="display: grid; gap: 24px;">
                    <!-- Barcode Row -->
                    <div style="display: grid; grid-template-columns: repeat(12, 1fr); gap: 16px;">
                        <div style="grid-column: span 3;">
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Barcode</label>
                            <div style="display: flex; gap: 8px;">
                                <input type="text" id="productBarcode" style="flex: 1; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;" placeholder="Enter barcode">
                                <button style="background-color: #4b5563; color: white; border: none; border-radius: 4px; padding: 4px 12px; font-size: 12px;">🔍</button>
                            </div>
                        </div>
                    </div>

                    <!-- Description Row -->
                    <div>
                        <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Description</label>
                        <textarea id="productDescription" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; resize: vertical; min-height: 100px;" placeholder="Enter product description"></textarea>
                    </div>

                    <!-- Category and Sub Category Row -->
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px;">
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Category</label>
                            <select id="productCategory" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">
                                <option value="">Select Category</option>
                                <option value="A-pipes">A. Pipes</option>
                                <option value="A-deli">A. Deli</option>
                                <option value="A-pills">A. Pills</option>
                                <option value="B-nailpolish">B. Nailpolish Remo</option>
                                <option value="black-unicorn">Black Unicorn</option>
                                <option value="C-misc">C. Miscellaneous</option>
                            </select>
                        </div>
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Sub Category</label>
                            <select id="productSubCategory" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">
                                <option value="">Select Sub Category</option>
                                <option value="water-pipes">Water Pipes</option>
                                <option value="dry-pipes">Dry Pipes</option>
                                <option value="metal-pipes">Metal Pipes</option>
                                <option value="glass-pipes">Glass Pipes</option>
                            </select>
                        </div>
                    </div>

                    <!-- Supplier and Purchase Price Row -->
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px;">
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Supplier</label>
                            <div style="display: flex; gap: 8px;">
                                <select id="productSupplier" style="flex: 1; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">
                                    <option value="">Select Supplier</option>
                                    <option value="SUP001">ABC Wholesale</option>
                                    <option value="SUP002">XYZ Distributors</option>
                                    <option value="SUP003">Premium Supplies Co</option>
                                </select>
                                <button style="background-color: #4b5563; color: white; border: none; border-radius: 4px; padding: 4px 12px; font-size: 12px;">🔍</button>
                            </div>
                        </div>
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Purchase Price</label>
                            <input type="number" step="0.01" id="productPurchasePrice" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="0.00">
                        </div>
                    </div>

                    <!-- Style and Color Row -->
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px;">
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Style</label>
                            <input type="text" id="productStyle" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="Enter style">
                        </div>
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Color</label>
                            <div style="display: flex; gap: 8px;">
                                <input type="text" id="productColor" style="flex: 1; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;" placeholder="Enter color">
                                <button style="background-color: #4b5563; color: white; border: none; border-radius: 4px; padding: 4px 12px; font-size: 12px;">▼</button>
                            </div>
                        </div>
                    </div>

                    <!-- Size, Min Qty, Max Qty Row -->
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 16px;">
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Size</label>
                            <input type="text" id="productSize" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="Enter size">
                        </div>
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Min Qty</label>
                            <input type="number" id="productMinQty" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="0">
                        </div>
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Max Qty</label>
                            <input type="number" id="productMaxQty" style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; width: 100%;" placeholder="0">
                        </div>
                    </div>

                    <!-- Image and Checkboxes Section -->
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 24px;">
                        <!-- Image Section -->
                        <div>
                            <label style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; display: block;">Image</label>
                            <div style="border: 2px dashed #d1d5db; border-radius: 8px; padding: 32px; text-align: center; background-color: #f9fafb;">
                                <div style="width: 48px; height: 48px; margin: 0 auto 16px; color: #9ca3af;">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 48 48">
                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                </div>
                                <p style="font-size: 14px; color: #6b7280; margin-bottom: 16px;">No image selected</p>
                                <div style="display: flex; gap: 8px; justify-content: center;">
                                    <button style="background-color: #2563eb; color: white; border: none; border-radius: 4px; padding: 4px 12px; font-size: 12px; font-weight: bold; cursor: pointer;">BROWSE</button>
                                    <button style="background-color: #4b5563; color: white; border: none; border-radius: 4px; padding: 4px 12px; font-size: 12px; font-weight: bold; cursor: pointer;">CLEAR</button>
                                </div>
                            </div>
                        </div>

                        <!-- Checkboxes Section -->
                        <div style="display: flex; flex-direction: column; gap: 16px;">
                            <div style="display: flex; align-items: center; gap: 12px;">
                                <input type="checkbox" id="productSpecialDiscount" style="width: 20px; height: 20px;">
                                <label for="productSpecialDiscount" style="font-size: 14px; font-weight: bold; color: #374151;">Special Discount</label>
                            </div>
                            <div style="display: flex; align-items: center; gap: 12px;">
                                <input type="checkbox" id="productPriority" style="width: 20px; height: 20px;">
                                <label for="productPriority" style="font-size: 14px; font-weight: bold; color: #374151;">Priority</label>
                            </div>
                            <div style="display: flex; align-items: center; gap: 12px;">
                                <input type="checkbox" id="productImageConfirm" style="width: 20px; height: 20px;">
                                <label for="productImageConfirm" style="font-size: 14px; font-weight: bold; color: #374151;">Image Confirm</label>
                            </div>
                            <div style="display: flex; align-items: center; gap: 12px;">
                                <input type="checkbox" id="productNonScanable" style="width: 20px; height: 20px;">
                                <label for="productNonScanable" style="font-size: 14px; font-weight: bold; color: #374151;">Non Scanable</label>
                            </div>
                            <div style="display: flex; align-items: center; gap: 12px;">
                                <input type="checkbox" id="productDeliItem" style="width: 20px; height: 20px;">
                                <label for="productDeliItem" style="font-size: 14px; font-weight: bold; color: #374151;">Deli Item</label>
                            </div>
                        </div>
                    </div>

                    <!-- Selling Price Section -->
                    <div>
                        <label style="font-size: 18px; font-weight: bold; color: #1f2937; margin-bottom: 16px; display: block;">Selling Price</label>
                        <div style="border: 1px solid #d1d5db; border-radius: 8px; overflow: hidden;">
                            <table style="width: 100%; border-collapse: collapse;">
                                <thead>
                                    <tr style="background-color: #f3f4f6; border-bottom: 1px solid #d1d5db;">
                                        <th style="text-align: left; padding: 12px; font-weight: bold; color: #374151; border-right: 1px solid #d1d5db;">Location</th>
                                        <th style="text-align: center; padding: 12px; font-weight: bold; color: #374151; border-right: 1px solid #d1d5db;">Stock</th>
                                        <th style="text-align: center; padding: 12px; font-weight: bold; color: #374151;">Price</th>
                                    </tr>
                                </thead>
                                <tbody id="productLocationStockTable">
                                    <!-- Table rows will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function renderReports() {
    return `
        <div>
            <h2 style="font-size: 32px; font-weight: 900; color: #1f2937; margin-bottom: 24px;">Reports & Analytics</h2>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 24px; margin-bottom: 32px;">
                <div style="background-color: #ffffff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                    <h3 style="font-size: 20px; font-weight: bold; color: #10b981; margin-bottom: 16px;">Sales Reports</h3>
                    <p style="color: #6b7280; margin-bottom: 16px;">Daily, weekly, and monthly sales analytics</p>
                    <div style="display: flex; flex-direction: column; gap: 8px;">
                        <button style="background-color: #10b981; color: white; border: none; padding: 12px 16px; border-radius: 4px; font-weight: bold; cursor: pointer; transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#059669'" onmouseout="this.style.backgroundColor='#10b981'">
                            Daily Sales Report
                        </button>
                        <button style="background-color: #059669; color: white; border: none; padding: 12px 16px; border-radius: 4px; font-weight: bold; cursor: pointer; transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#047857'" onmouseout="this.style.backgroundColor='#059669'">
                            Weekly Sales Report
                        </button>
                        <button style="background-color: #047857; color: white; border: none; padding: 12px 16px; border-radius: 4px; font-weight: bold; cursor: pointer; transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#065f46'" onmouseout="this.style.backgroundColor='#047857'">
                            Monthly Sales Report
                        </button>
                    </div>
                </div>

                <div style="background-color: #ffffff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                    <h3 style="font-size: 20px; font-weight: bold; color: #3b82f6; margin-bottom: 16px;">Inventory Reports</h3>
                    <p style="color: #6b7280; margin-bottom: 16px;">Stock levels and inventory movement reports</p>
                    <div style="display: flex; flex-direction: column; gap: 8px;">
                        <button style="background-color: #3b82f6; color: white; border: none; padding: 12px 16px; border-radius: 4px; font-weight: bold; cursor: pointer; transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#2563eb'" onmouseout="this.style.backgroundColor='#3b82f6'">
                            Current Stock Report
                        </button>
                        <button style="background-color: #2563eb; color: white; border: none; padding: 12px 16px; border-radius: 4px; font-weight: bold; cursor: pointer; transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#1d4ed8'" onmouseout="this.style.backgroundColor='#2563eb'">
                            Low Stock Alert
                        </button>
                        <button style="background-color: #1d4ed8; color: white; border: none; padding: 12px 16px; border-radius: 4px; font-weight: bold; cursor: pointer; transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#1e40af'" onmouseout="this.style.backgroundColor='#1d4ed8'">
                            Inventory Movement
                        </button>
                    </div>
                </div>

                <div style="background-color: #ffffff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                    <h3 style="font-size: 20px; font-weight: bold; color: #f59e0b; margin-bottom: 16px;">Customer Reports</h3>
                    <p style="color: #6b7280; margin-bottom: 16px;">Customer analytics and behavior reports</p>
                    <div style="display: flex; flex-direction: column; gap: 8px;">
                        <button style="background-color: #f59e0b; color: white; border: none; padding: 12px 16px; border-radius: 4px; font-weight: bold; cursor: pointer; transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#d97706'" onmouseout="this.style.backgroundColor='#f59e0b'">
                            Customer Analysis
                        </button>
                        <button style="background-color: #d97706; color: white; border: none; padding: 12px 16px; border-radius: 4px; font-weight: bold; cursor: pointer; transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#b45309'" onmouseout="this.style.backgroundColor='#d97706'">
                            Purchase History
                        </button>
                        <button style="background-color: #b45309; color: white; border: none; padding: 12px 16px; border-radius: 4px; font-weight: bold; cursor: pointer; transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#92400e'" onmouseout="this.style.backgroundColor='#b45309'">
                            Customer Trends
                        </button>
                    </div>
                </div>

                <div style="background-color: #ffffff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                    <h3 style="font-size: 20px; font-weight: bold; color: #ef4444; margin-bottom: 16px;">Financial Reports</h3>
                    <p style="color: #6b7280; margin-bottom: 16px;">Revenue, profit, and financial analytics</p>
                    <div style="display: flex; flex-direction: column; gap: 8px;">
                        <button style="background-color: #ef4444; color: white; border: none; padding: 12px 16px; border-radius: 4px; font-weight: bold; cursor: pointer; transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#dc2626'" onmouseout="this.style.backgroundColor='#ef4444'">
                            Revenue Report
                        </button>
                        <button style="background-color: #dc2626; color: white; border: none; padding: 12px 16px; border-radius: 4px; font-weight: bold; cursor: pointer; transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#b91c1c'" onmouseout="this.style.backgroundColor='#dc2626'">
                            Profit & Loss
                        </button>
                        <button style="background-color: #b91c1c; color: white; border: none; padding: 12px 16px; border-radius: 4px; font-weight: bold; cursor: pointer; transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#991b1b'" onmouseout="this.style.backgroundColor='#b91c1c'">
                            Tax Report
                        </button>
                    </div>
                </div>

                <div style="background-color: #ffffff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                    <h3 style="font-size: 20px; font-weight: bold; color: #8b5cf6; margin-bottom: 16px;">Product Reports</h3>
                    <p style="color: #6b7280; margin-bottom: 16px;">Product performance and category analysis</p>
                    <div style="display: flex; flex-direction: column; gap: 8px;">
                        <button style="background-color: #8b5cf6; color: white; border: none; padding: 12px 16px; border-radius: 4px; font-weight: bold; cursor: pointer; transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#7c3aed'" onmouseout="this.style.backgroundColor='#8b5cf6'">
                            Top Products
                        </button>
                        <button style="background-color: #7c3aed; color: white; border: none; padding: 12px 16px; border-radius: 4px; font-weight: bold; cursor: pointer; transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#6d28d9'" onmouseout="this.style.backgroundColor='#7c3aed'">
                            Category Performance
                        </button>
                        <button style="background-color: #6d28d9; color: white; border: none; padding: 12px 16px; border-radius: 4px; font-weight: bold; cursor: pointer; transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#5b21b6'" onmouseout="this.style.backgroundColor='#6d28d9'">
                            Product Trends
                        </button>
                    </div>
                </div>

                <div style="background-color: #ffffff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                    <h3 style="font-size: 20px; font-weight: bold; color: #6366f1; margin-bottom: 16px;">Custom Reports</h3>
                    <p style="color: #6b7280; margin-bottom: 16px;">Create and manage custom report templates</p>
                    <div style="display: flex; flex-direction: column; gap: 8px;">
                        <button style="background-color: #6366f1; color: white; border: none; padding: 12px 16px; border-radius: 4px; font-weight: bold; cursor: pointer; transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#4f46e5'" onmouseout="this.style.backgroundColor='#6366f1'">
                            Report Builder
                        </button>
                        <button style="background-color: #4f46e5; color: white; border: none; padding: 12px 16px; border-radius: 4px; font-weight: bold; cursor: pointer; transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#4338ca'" onmouseout="this.style.backgroundColor='#4f46e5'">
                            Saved Reports
                        </button>
                        <button style="background-color: #4338ca; color: white; border: none; padding: 12px 16px; border-radius: 4px; font-weight: bold; cursor: pointer; transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#3730a3'" onmouseout="this.style.backgroundColor='#4338ca'">
                            Schedule Reports
                        </button>
                    </div>
                </div>
            </div>

            <div style="background-color: #ffffff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); margin-bottom: 24px;">
                <h3 style="font-size: 20px; font-weight: bold; color: #1f2937; margin-bottom: 16px;">Quick Stats</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                    <div style="text-align: center; padding: 16px; background-color: #ecfdf5; border-radius: 8px;">
                        <div style="font-size: 24px; font-weight: 900; color: #10b981;">$12,450</div>
                        <div style="font-size: 14px; color: #6b7280;">This Week Sales</div>
                    </div>
                    <div style="text-align: center; padding: 16px; background-color: #eff6ff; border-radius: 8px;">
                        <div style="font-size: 24px; font-weight: 900; color: #3b82f6;">342</div>
                        <div style="font-size: 14px; color: #6b7280;">Products Sold</div>
                    </div>
                    <div style="text-align: center; padding: 16px; background-color: #fffbeb; border-radius: 8px;">
                        <div style="font-size: 24px; font-weight: 900; color: #f59e0b;">89</div>
                        <div style="font-size: 14px; color: #6b7280;">Active Customers</div>
                    </div>
                    <div style="text-align: center; padding: 16px; background-color: #fef2f2; border-radius: 8px;">
                        <div style="font-size: 24px; font-weight: 900; color: #ef4444;">15</div>
                        <div style="font-size: 14px; color: #6b7280;">Low Stock Items</div>
                    </div>
                </div>
            </div>

            <div style="background-color: #ffffff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                <h3 style="font-size: 20px; font-weight: bold; color: #1f2937; margin-bottom: 16px;">Recent Report Activity</h3>
                <div style="display: flex; flex-direction: column; gap: 12px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background-color: #ecfdf5; border: 1px solid #d1fae5; border-radius: 4px;">
                        <div>
                            <span style="color: #065f46; font-weight: 500;">Daily Sales Report Generated</span>
                            <div style="font-size: 14px; color: #6b7280;">Report ID: RPT-2024-001</div>
                        </div>
                        <span style="color: #6b7280; font-size: 14px;">5 minutes ago</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background-color: #eff6ff; border: 1px solid #dbeafe; border-radius: 4px;">
                        <div>
                            <span style="color: #1e40af; font-weight: 500;">Inventory Report Scheduled</span>
                            <div style="font-size: 14px; color: #6b7280;">Next run: Tomorrow 9:00 AM</div>
                        </div>
                        <span style="color: #6b7280; font-size: 14px;">1 hour ago</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background-color: #fffbeb; border: 1px solid #fed7aa; border-radius: 4px;">
                        <div>
                            <span style="color: #92400e; font-weight: 500;">Customer Analysis Completed</span>
                            <div style="font-size: 14px; color: #6b7280;">Report ID: RPT-2024-002</div>
                        </div>
                        <span style="color: #6b7280; font-size: 14px;">3 hours ago</span>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function renderTransactions() {
    return `
        <div>
            <h2 style="font-size: 32px; font-weight: 900; color: #1f2937; margin-bottom: 24px;">Transaction Management</h2>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 24px; margin-bottom: 32px;">
                <div style="background-color: #ffffff; border: 2px solid #10b981; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); text-align: center;">
                    <h3 style="font-size: 18px; font-weight: bold; color: #10b981; margin-bottom: 8px;">Today's Transactions</h3>
                    <p style="font-size: 32px; font-weight: 900; color: #1f2937;">127</p>
                    <p style="font-size: 14px; color: #6b7280;">+12% from yesterday</p>
                </div>
                <div style="background-color: #ffffff; border: 2px solid #3b82f6; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); text-align: center;">
                    <h3 style="font-size: 18px; font-weight: bold; color: #3b82f6; margin-bottom: 8px;">Total Amount</h3>
                    <p style="font-size: 32px; font-weight: 900; color: #1f2937;">$2,450.75</p>
                    <p style="font-size: 14px; color: #6b7280;">+8% from yesterday</p>
                </div>
                <div style="background-color: #ffffff; border: 2px solid #f59e0b; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); text-align: center;">
                    <h3 style="font-size: 18px; font-weight: bold; color: #f59e0b; margin-bottom: 8px;">Average Sale</h3>
                    <p style="font-size: 32px; font-weight: 900; color: #1f2937;">$19.30</p>
                    <p style="font-size: 14px; color: #6b7280;">-2% from yesterday</p>
                </div>
                <div style="background-color: #ffffff; border: 2px solid #ef4444; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); text-align: center;">
                    <h3 style="font-size: 18px; font-weight: bold; color: #ef4444; margin-bottom: 8px;">Refunds</h3>
                    <p style="font-size: 32px; font-weight: 900; color: #1f2937;">3</p>
                    <p style="font-size: 14px; color: #6b7280;">Same as yesterday</p>
                </div>
            </div>

            <div style="background-color: #ffffff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); margin-bottom: 24px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                    <h3 style="font-size: 20px; font-weight: bold; color: #1f2937;">Recent Transactions</h3>
                    <div style="display: flex; gap: 8px;">
                        <button style="background-color: #10b981; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-weight: bold; cursor: pointer; transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#059669'" onmouseout="this.style.backgroundColor='#10b981'">
                            Export CSV
                        </button>
                        <button style="background-color: #3b82f6; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-weight: bold; cursor: pointer; transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#2563eb'" onmouseout="this.style.backgroundColor='#3b82f6'">
                            Filter
                        </button>
                    </div>
                </div>

                <div style="overflow-x: auto;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="border-bottom: 2px solid #e5e7eb;">
                                <th style="color: #374151; padding: 12px; font-weight: bold; text-align: left;">Transaction ID</th>
                                <th style="color: #374151; padding: 12px; font-weight: bold; text-align: left;">Date & Time</th>
                                <th style="color: #374151; padding: 12px; font-weight: bold; text-align: left;">Customer</th>
                                <th style="color: #374151; padding: 12px; font-weight: bold; text-align: left;">Items</th>
                                <th style="color: #374151; padding: 12px; font-weight: bold; text-align: left;">Amount</th>
                                <th style="color: #374151; padding: 12px; font-weight: bold; text-align: left;">Payment Method</th>
                                <th style="color: #374151; padding: 12px; font-weight: bold; text-align: left;">Status</th>
                                <th style="color: #374151; padding: 12px; font-weight: bold; text-align: left;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="border-bottom: 1px solid #f3f4f6;" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='transparent'">
                                <td style="padding: 12px; color: #3b82f6; font-weight: 500;">#TXN-001</td>
                                <td style="padding: 12px; color: #1f2937;">2024-01-15 14:30</td>
                                <td style="padding: 12px; color: #1f2937;">John Smith</td>
                                <td style="padding: 12px; color: #6b7280;">3 items</td>
                                <td style="padding: 12px; color: #10b981; font-weight: bold;">$45.99</td>
                                <td style="padding: 12px; color: #6b7280;">Credit Card</td>
                                <td style="padding: 12px;">
                                    <span style="background-color: #dcfce7; color: #166534; padding: 4px 8px; border-radius: 9999px; font-size: 12px; font-weight: bold;">
                                        Completed
                                    </span>
                                </td>
                                <td style="padding: 12px;">
                                    <button style="background-color: #6b7280; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 12px; cursor: pointer;" onmouseover="this.style.backgroundColor='#4b5563'" onmouseout="this.style.backgroundColor='#6b7280'">
                                        View Details
                                    </button>
                                </td>
                            </tr>
                            <tr style="border-bottom: 1px solid #f3f4f6;" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='transparent'">
                                <td style="padding: 12px; color: #3b82f6; font-weight: 500;">#TXN-002</td>
                                <td style="padding: 12px; color: #1f2937;">2024-01-15 14:25</td>
                                <td style="padding: 12px; color: #1f2937;">Sarah Johnson</td>
                                <td style="padding: 12px; color: #6b7280;">1 item</td>
                                <td style="padding: 12px; color: #10b981; font-weight: bold;">$23.50</td>
                                <td style="padding: 12px; color: #6b7280;">Cash</td>
                                <td style="padding: 12px;">
                                    <span style="background-color: #dcfce7; color: #166534; padding: 4px 8px; border-radius: 9999px; font-size: 12px; font-weight: bold;">
                                        Completed
                                    </span>
                                </td>
                                <td style="padding: 12px;">
                                    <button style="background-color: #6b7280; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 12px; cursor: pointer;" onmouseover="this.style.backgroundColor='#4b5563'" onmouseout="this.style.backgroundColor='#6b7280'">
                                        View Details
                                    </button>
                                </td>
                            </tr>
                            <tr style="border-bottom: 1px solid #f3f4f6;" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='transparent'">
                                <td style="padding: 12px; color: #3b82f6; font-weight: 500;">#TXN-003</td>
                                <td style="padding: 12px; color: #1f2937;">2024-01-15 14:20</td>
                                <td style="padding: 12px; color: #1f2937;">Mike Davis</td>
                                <td style="padding: 12px; color: #6b7280;">5 items</td>
                                <td style="padding: 12px; color: #10b981; font-weight: bold;">$78.25</td>
                                <td style="padding: 12px; color: #6b7280;">Debit Card</td>
                                <td style="padding: 12px;">
                                    <span style="background-color: #dcfce7; color: #166534; padding: 4px 8px; border-radius: 9999px; font-size: 12px; font-weight: bold;">
                                        Completed
                                    </span>
                                </td>
                                <td style="padding: 12px;">
                                    <button style="background-color: #6b7280; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 12px; cursor: pointer;" onmouseover="this.style.backgroundColor='#4b5563'" onmouseout="this.style.backgroundColor='#6b7280'">
                                        View Details
                                    </button>
                                </td>
                            </tr>
                            <tr style="border-bottom: 1px solid #f3f4f6;" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='transparent'">
                                <td style="padding: 12px; color: #3b82f6; font-weight: 500;">#TXN-004</td>
                                <td style="padding: 12px; color: #1f2937;">2024-01-15 14:15</td>
                                <td style="padding: 12px; color: #1f2937;">Lisa Wilson</td>
                                <td style="padding: 12px; color: #6b7280;">2 items</td>
                                <td style="padding: 12px; color: #ef4444; font-weight: bold;">-$15.99</td>
                                <td style="padding: 12px; color: #6b7280;">Credit Card</td>
                                <td style="padding: 12px;">
                                    <span style="background-color: #fee2e2; color: #991b1b; padding: 4px 8px; border-radius: 9999px; font-size: 12px; font-weight: bold;">
                                        Refunded
                                    </span>
                                </td>
                                <td style="padding: 12px;">
                                    <button style="background-color: #6b7280; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 12px; cursor: pointer;" onmouseover="this.style.backgroundColor='#4b5563'" onmouseout="this.style.backgroundColor='#6b7280'">
                                        View Details
                                    </button>
                                </td>
                            </tr>
                            <tr style="border-bottom: 1px solid #f3f4f6;" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='transparent'">
                                <td style="padding: 12px; color: #3b82f6; font-weight: 500;">#TXN-005</td>
                                <td style="padding: 12px; color: #1f2937;">2024-01-15 14:10</td>
                                <td style="padding: 12px; color: #1f2937;">Robert Brown</td>
                                <td style="padding: 12px; color: #6b7280;">4 items</td>
                                <td style="padding: 12px; color: #f59e0b; font-weight: bold;">$32.75</td>
                                <td style="padding: 12px; color: #6b7280;">Cash</td>
                                <td style="padding: 12px;">
                                    <span style="background-color: #fef3c7; color: #92400e; padding: 4px 8px; border-radius: 9999px; font-size: 12px; font-weight: bold;">
                                        Pending
                                    </span>
                                </td>
                                <td style="padding: 12px;">
                                    <button style="background-color: #6b7280; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 12px; cursor: pointer;" onmouseover="this.style.backgroundColor='#4b5563'" onmouseout="this.style.backgroundColor='#6b7280'">
                                        View Details
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 16px; padding-top: 16px; border-top: 1px solid #e5e7eb;">
                    <div style="font-size: 14px; color: #6b7280;">
                        Showing 5 of 127 transactions
                    </div>
                    <div style="display: flex; gap: 8px;">
                        <button style="background-color: #d1d5db; color: #374151; border: none; padding: 4px 12px; border-radius: 4px; font-size: 14px; cursor: pointer;" onmouseover="this.style.backgroundColor='#9ca3af'" onmouseout="this.style.backgroundColor='#d1d5db'">
                            Previous
                        </button>
                        <button style="background-color: #10b981; color: white; border: none; padding: 4px 12px; border-radius: 4px; font-size: 14px; cursor: pointer;" onmouseover="this.style.backgroundColor='#059669'" onmouseout="this.style.backgroundColor='#10b981'">
                            Next
                        </button>
                    </div>
                </div>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 24px;">
                <div style="background-color: #ffffff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                    <h3 style="font-size: 20px; font-weight: bold; color: #1f2937; margin-bottom: 16px;">Payment Methods</h3>
                    <div style="display: flex; flex-direction: column; gap: 12px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="color: #374151;">Credit Card</span>
                            <span style="color: #10b981; font-weight: bold;">65%</span>
                        </div>
                        <div style="width: 100%; background-color: #e5e7eb; border-radius: 9999px; height: 8px;">
                            <div style="background-color: #10b981; height: 8px; border-radius: 9999px; width: 65%;"></div>
                        </div>

                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="color: #374151;">Cash</span>
                            <span style="color: #3b82f6; font-weight: bold;">25%</span>
                        </div>
                        <div style="width: 100%; background-color: #e5e7eb; border-radius: 9999px; height: 8px;">
                            <div style="background-color: #3b82f6; height: 8px; border-radius: 9999px; width: 25%;"></div>
                        </div>

                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="color: #374151;">Debit Card</span>
                            <span style="color: #f59e0b; font-weight: bold;">10%</span>
                        </div>
                        <div style="width: 100%; background-color: #e5e7eb; border-radius: 9999px; height: 8px;">
                            <div style="background-color: #f59e0b; height: 8px; border-radius: 9999px; width: 10%;"></div>
                        </div>
                    </div>
                </div>

                <div style="background-color: #ffffff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                    <h3 style="font-size: 20px; font-weight: bold; color: #1f2937; margin-bottom: 16px;">Transaction Actions</h3>
                    <div style="display: flex; flex-direction: column; gap: 12px;">
                        <button style="background-color: #10b981; color: white; border: none; padding: 12px 16px; border-radius: 4px; font-weight: bold; cursor: pointer; transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#059669'" onmouseout="this.style.backgroundColor='#10b981'">
                            Process Refund
                        </button>
                        <button style="background-color: #3b82f6; color: white; border: none; padding: 12px 16px; border-radius: 4px; font-weight: bold; cursor: pointer; transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#2563eb'" onmouseout="this.style.backgroundColor='#3b82f6'">
                            Void Transaction
                        </button>
                        <button style="background-color: #f59e0b; color: white; border: none; padding: 12px 16px; border-radius: 4px; font-weight: bold; cursor: pointer; transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#d97706'" onmouseout="this.style.backgroundColor='#f59e0b'">
                            Print Receipt
                        </button>
                        <button style="background-color: #8b5cf6; color: white; border: none; padding: 12px 16px; border-radius: 4px; font-weight: bold; cursor: pointer; transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#7c3aed'" onmouseout="this.style.backgroundColor='#8b5cf6'">
                            Send Email Receipt
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function renderWholesale() {
    return `
        <div>
            <h2 style="font-size: 32px; font-weight: 900; color: #1f2937; margin-bottom: 24px;">Wholesale</h2>
            <div style="background-color: #ffffff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                <p style="font-size: 18px; color: #6b7280; text-align: center; padding: 48px;">
                    Wholesale management functionality will be implemented here.
                </p>
            </div>
        </div>
    `;
}

function renderUserManagement() {
    // Initialize user management when rendering
    setTimeout(() => {
        initializeUserManagement();
    }, 100);

    return `
        <div style="padding: 24px;">
            <h2 style="font-size: 32px; font-weight: 900; color: #1f2937; margin-bottom: 24px;">User Management System</h2>

            <!-- User Management Overview Cards -->
            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 24px; margin-bottom: 32px;">
                <!-- Admin Users Card -->
                <div style="background-color: #ffffff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                    <div style="display: flex; items-center; justify-content: space-between; margin-bottom: 16px;">
                        <h3 style="font-size: 18px; font-weight: bold; color: #059669;">Admin Users</h3>
                        <div style="background-color: #d1fae5; color: #065f46; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: bold;">
                            Full Access
                        </div>
                    </div>
                    <p id="admin-count" style="font-size: 32px; font-weight: 900; color: #1f2937; margin-bottom: 8px;">0</p>
                    <p style="font-size: 14px; color: #6b7280;">Active admin accounts</p>
                </div>

                <!-- Cashier Users Card -->
                <div style="background-color: #ffffff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                    <div style="display: flex; items-center; justify-content: space-between; margin-bottom: 16px;">
                        <h3 style="font-size: 18px; font-weight: bold; color: #2563eb;">Cashier Users</h3>
                        <div style="background-color: #dbeafe; color: #1e40af; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: bold;">
                            Limited Access
                        </div>
                    </div>
                    <p id="cashier-count" style="font-size: 32px; font-weight: 900; color: #1f2937; margin-bottom: 8px;">0</p>
                    <p style="font-size: 14px; color: #6b7280;">Active cashier accounts</p>
                </div>

                <!-- CCTV Users Card -->
                <div style="background-color: #ffffff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                    <div style="display: flex; items-center; justify-content: space-between; margin-bottom: 16px;">
                        <h3 style="font-size: 18px; font-weight: bold; color: #dc2626;">CCTV Users</h3>
                        <div style="background-color: #fee2e2; color: #991b1b; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: bold;">
                            View Only
                        </div>
                    </div>
                    <p id="cctv-count" style="font-size: 32px; font-weight: 900; color: #1f2937; margin-bottom: 8px;">0</p>
                    <p style="font-size: 14px; color: #6b7280;">Active CCTV accounts</p>
                </div>
            </div>

            <!-- Action Buttons -->
            <div style="display: flex; gap: 12px; margin-bottom: 24px; flex-wrap: wrap;">
                <button onclick="showAddUserModal('Admin')" style="background-color: #059669; color: white; border: none; border-radius: 8px; padding: 12px 24px; font-size: 14px; font-weight: bold; cursor: pointer;">
                    + Add New Admin
                </button>
                <button onclick="showAddUserModal('Cashier')" style="background-color: #2563eb; color: white; border: none; border-radius: 8px; padding: 12px 24px; font-size: 14px; font-weight: bold; cursor: pointer;">
                    + Add New Cashier
                </button>
                <button onclick="showAddUserModal('CCTV')" style="background-color: #dc2626; color: white; border: none; border-radius: 8px; padding: 12px 24px; font-size: 14px; font-weight: bold; cursor: pointer;">
                    + Add New CCTV User
                </button>
                <button onclick="forceCreatePermissionsTable()" style="background-color: #f59e0b; color: white; border: none; border-radius: 8px; padding: 12px 24px; font-size: 14px; font-weight: bold; cursor: pointer;">
                    🔧 Fix Permissions Table
                </button>
                <button onclick="debugUserPermissions()" style="background-color: #8b5cf6; color: white; border: none; border-radius: 8px; padding: 12px 24px; font-size: 14px; font-weight: bold; cursor: pointer;">
                    🐛 Debug User Permissions
                </button>
            </div>

            <!-- User Management Table -->
            <div style="background-color: #ffffff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 24px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                <div style="margin-bottom: 16px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                        <h3 style="font-size: 20px; font-weight: bold; color: #1f2937;">All Users</h3>
                        <button onclick="clearAllFilters()" style="background-color: #6b7280; color: white; border: none; border-radius: 6px; padding: 8px 16px; font-size: 14px; font-weight: bold; cursor: pointer;">
                            Clear Filters
                        </button>
                    </div>

                    <!-- Filter Controls -->
                    <div style="display: grid; grid-template-columns: 2fr 1fr 1fr 1fr auto; gap: 12px; align-items: end; background-color: #f9fafb; padding: 16px; border-radius: 8px; border: 1px solid #e5e7eb;">
                        <!-- Search Input -->
                        <div>
                            <label style="display: block; font-size: 12px; font-weight: bold; color: #374151; margin-bottom: 4px;">Search Users</label>
                            <input type="text" id="searchInput" placeholder="Search by name or username..." style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; background-color: white; color: #1f2937; outline: none; box-sizing: border-box; -webkit-appearance: none; -moz-appearance: none; appearance: none;">
                        </div>

                        <!-- Role Filter -->
                        <div>
                            <label style="display: block; font-size: 12px; font-weight: bold; color: #374151; margin-bottom: 4px;">Filter by Role</label>
                            <select id="roleFilter" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                                <option value="">All Roles</option>
                                <option value="Admin">Admin</option>
                                <option value="Cashier">Cashier</option>
                                <option value="CCTV">CCTV</option>
                            </select>
                        </div>

                        <!-- Status Filter -->
                        <div>
                            <label style="display: block; font-size: 12px; font-weight: bold; color: #374151; margin-bottom: 4px;">Filter by Status</label>
                            <select id="statusFilter" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                                <option value="">All Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>

                        <!-- Permission Filter -->
                        <div>
                            <label style="display: block; font-size: 12px; font-weight: bold; color: #374151; margin-bottom: 4px;">Filter by Permission</label>
                            <select id="permissionFilter" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                                <option value="">All Permissions</option>
                                <option value="Full Access">Full Access</option>
                                <option value="POS, Theater">POS, Theater</option>
                                <option value="View Only">View Only</option>
                            </select>
                        </div>

                        <!-- Filter Results Count -->
                        <div style="text-align: center;">
                            <div style="font-size: 12px; font-weight: bold; color: #374151; margin-bottom: 4px;">Results</div>
                            <div id="filterResultCount" style="font-size: 18px; font-weight: 900; color: #059669;">0</div>
                        </div>
                    </div>
                </div>

                <div style="overflow-x: auto;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background-color: #f9fafb; border-bottom: 2px solid #e5e7eb;">
                                <th style="padding: 12px; text-align: left; font-weight: bold; color: #374151;">Username</th>
                                <th style="padding: 12px; text-align: left; font-weight: bold; color: #374151;">Full Name</th>
                                <th style="padding: 12px; text-align: left; font-weight: bold; color: #374151;">Role</th>
                                <th style="padding: 12px; text-align: left; font-weight: bold; color: #374151;">Status</th>
                                <th style="padding: 12px; text-align: left; font-weight: bold; color: #374151;">Last Login</th>
                                <th style="padding: 12px; text-align: left; font-weight: bold; color: #374151;">Permissions</th>
                                <th style="padding: 12px; text-align: left; font-weight: bold; color: #374151;">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="userTableBody">
                            <!-- Users will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Add User Modal -->
            <div id="addUserModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 1000; justify-content: center; align-items: center;">
                <div style="background-color: white; border-radius: 8px; padding: 24px; width: 90%; max-width: 700px; max-height: 90%; overflow-y: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h3 id="modalTitle" style="font-size: 20px; font-weight: bold; color: #1f2937;">Add New User</h3>
                        <button onclick="closeAddUserModal()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #6b7280;">&times;</button>
                    </div>

                    <form id="addUserForm" onsubmit="handleAddUser(event)">
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px;">Full Name</label>
                            <input type="text" id="userName" required style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;" placeholder="Enter full name">
                        </div>

                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px;">Username</label>
                            <input type="text" id="userUsername" required style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;" placeholder="Enter username">
                        </div>

                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px;">Password</label>
                            <input type="password" id="userPassword" required style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;" placeholder="Enter password">
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px;">Role</label>
                            <select id="userRole" required onchange="toggleAccessControl()" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">
                                <option value="">Select Role</option>
                                <option value="Admin">Admin</option>
                                <option value="Cashier">Cashier</option>
                                <option value="CCTV">CCTV</option>
                            </select>
                        </div>

                        <!-- Access Control Section (shown only for Cashier and CCTV) -->
                        <div id="accessControlSection" style="display: none; margin-bottom: 20px; border: 1px solid #e5e7eb; border-radius: 8px; padding: 16px; background-color: #f9fafb;">
                            <h4 style="font-size: 16px; font-weight: bold; color: #374151; margin-bottom: 12px;">Access Control</h4>
                            <p style="font-size: 12px; color: #6b7280; margin-bottom: 16px;">Select which modules this user can access and their permission levels.</p>

                            <div id="accessControlList">
                                <!-- Access control checkboxes will be populated here -->
                            </div>
                        </div>

                        <div style="display: flex; gap: 12px; justify-content: flex-end;">
                            <button type="button" onclick="closeAddUserModal()" style="background-color: #6b7280; color: white; border: none; border-radius: 6px; padding: 10px 20px; font-size: 14px; font-weight: bold; cursor: pointer;">
                                Cancel
                            </button>
                            <button type="submit" style="background-color: #059669; color: white; border: none; border-radius: 6px; padding: 10px 20px; font-size: 14px; font-weight: bold; cursor: pointer;">
                                Add User
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Edit User Modal -->
            <div id="editUserModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 1000; justify-content: center; align-items: center;">
                <div style="background-color: white; border-radius: 8px; padding: 24px; width: 90%; max-width: 700px; max-height: 90%; overflow-y: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h3 style="font-size: 20px; font-weight: bold; color: #1f2937;">Edit User</h3>
                        <button onclick="closeEditUserModal()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #6b7280;">&times;</button>
                    </div>

                    <form id="editUserForm" onsubmit="handleEditUser(event)">
                        <input type="hidden" id="editUserId">

                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px;">Full Name</label>
                            <input type="text" id="editUserName" required style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;" placeholder="Enter full name">
                        </div>

                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px;">Username</label>
                            <input type="text" id="editUserUsername" required style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;" placeholder="Enter username">
                        </div>

                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px;">Role</label>
                            <select id="editUserRole" required style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">
                                <option value="">Select Role</option>
                                <option value="Admin">Admin</option>
                                <option value="Cashier">Cashier</option>
                                <option value="CCTV">CCTV</option>
                            </select>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px;">Status</label>
                            <select id="editUserStatus" required style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px;">New Password (leave blank to keep current)</label>
                            <input type="password" id="editUserPassword" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;" placeholder="Enter new password">
                        </div>

                        <!-- Access Control Section for Edit -->
                        <div id="editAccessControlSection" style="display: none; margin-bottom: 20px; padding: 16px; border: 1px solid #e5e7eb; border-radius: 8px; background-color: #f9fafb;">
                            <h4 style="font-size: 16px; font-weight: bold; color: #374151; margin-bottom: 12px;">Access Control</h4>
                            <div id="editAccessControlList">
                                <!-- Access control checkboxes will be populated here -->
                            </div>
                        </div>

                        <div style="display: flex; gap: 12px; justify-content: flex-end;">
                            <button type="button" onclick="closeEditUserModal()" style="background-color: #6b7280; color: white; border: none; border-radius: 6px; padding: 10px 20px; font-size: 14px; font-weight: bold; cursor: pointer;">
                                Cancel
                            </button>
                            <button type="submit" style="background-color: #2563eb; color: white; border: none; border-radius: 6px; padding: 10px 20px; font-size: 14px; font-weight: bold; cursor: pointer;">
                                Update User
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;
}



// Category Management Functions
let categories = [
    {
        code: "A001",
        name: "A. Pipes",
        parent: "-",
        productsCount: 5,
        status: "active"
    },
    {
        code: "A002",
        name: "Water Pipes",
        parent: "A. Pipes",
        productsCount: 2,
        status: "active"
    },
    {
        code: "A003",
        name: "A. Deli",
        parent: "-",
        productsCount: 5,
        status: "active"
    },
    {
        code: "B001",
        name: "B. Nailpolish Remo",
        parent: "-",
        productsCount: 5,
        status: "active"
    }
];

let hierarchyData = [
    {
        name: "A. Pipes",
        children: ["Water Pipes", "Dry Pipes", "Metal Pipes"]
    },
    {
        name: "A. Deli",
        children: ["Sandwiches", "Salads", "Hot Items"]
    },
    {
        name: "A. Pills",
        children: ["Vitamins", "Pain Relief", "Supplements"]
    },
    {
        name: "B. Nailpolish Remo",
        children: ["Removers", "Pads", "Cuticle Care"]
    }
];

function initializeCategoryPage() {
    renderCategoryHierarchy();
    renderCategoryTable();
}

function addCategory() {
    const categoryName = document.getElementById('categoryName').value;
    const categoryCode = document.getElementById('categoryCode').value;
    const parentCategory = document.getElementById('parentCategory').value;
    const categoryDescription = document.getElementById('categoryDescription').value;
    const categoryStatus = document.getElementById('categoryStatus').value;

    if (!categoryName || !categoryCode) {
        alert('Please fill in required fields: Category Name and Category Code');
        return;
    }

    // Check if code already exists
    if (categories.find(cat => cat.code === categoryCode)) {
        alert('Category code already exists');
        return;
    }

    const newCategory = {
        code: categoryCode,
        name: categoryName,
        parent: parentCategory || "-",
        productsCount: 0,
        status: categoryStatus,
        description: categoryDescription
    };

    categories.push(newCategory);
    renderCategoryTable();
    clearCategoryForm();

    console.log('Category added:', newCategory);
}

function clearCategoryForm() {
    document.getElementById('categoryName').value = '';
    document.getElementById('categoryCode').value = '';
    document.getElementById('parentCategory').value = '';
    document.getElementById('categoryDescription').value = '';
    document.getElementById('categoryStatus').value = 'active';
}

function editCategory(code) {
    const category = categories.find(cat => cat.code === code);
    if (category) {
        document.getElementById('categoryName').value = category.name;
        document.getElementById('categoryCode').value = category.code;
        document.getElementById('parentCategory').value = category.parent === "-" ? "" : category.parent;
        document.getElementById('categoryDescription').value = category.description || '';
        document.getElementById('categoryStatus').value = category.status;
    }
}

function deleteCategory(code) {
    if (confirm('Are you sure you want to delete this category?')) {
        categories = categories.filter(cat => cat.code !== code);
        renderCategoryTable();
        console.log('Category deleted:', code);
    }
}

function importCategories() {
    console.log('Import categories functionality');
    alert('Import categories functionality will be implemented');
}

function exportCategories() {
    console.log('Export categories functionality');
    alert('Export categories functionality will be implemented');
}

function renderCategoryHierarchy() {
    const container = document.getElementById('categoryHierarchy');
    if (!container) return;

    container.innerHTML = '';

    hierarchyData.forEach(item => {
        const hierarchyItem = document.createElement('div');
        hierarchyItem.style.cssText = 'border: 1px solid #e5e7eb; border-radius: 8px; padding: 12px; margin-bottom: 12px;';

        hierarchyItem.innerHTML = `
            <div style="font-weight: bold; color: #1f2937; margin-bottom: 8px;">${item.name}</div>
            <div style="margin-left: 16px;">
                ${item.children.map(child => `<div style="font-size: 14px; color: #6b7280; margin-bottom: 4px;">• ${child}</div>`).join('')}
            </div>
        `;

        container.appendChild(hierarchyItem);
    });
}

function renderCategoryTable() {
    const tbody = document.getElementById('categoryTableBody');
    if (!tbody) return;

    tbody.innerHTML = '';

    categories.forEach(category => {
        const row = document.createElement('tr');
        row.style.cssText = 'border-bottom: 1px solid #f3f4f6;';
        row.innerHTML = `
            <td style="padding: 12px; color: #2563eb; font-weight: 500;">${category.code}</td>
            <td style="padding: 12px; color: #1f2937;">${category.name}</td>
            <td style="padding: 12px; color: #6b7280;">${category.parent}</td>
            <td style="padding: 12px; color: #6b7280;">${category.productsCount} products</td>
            <td style="padding: 12px;">
                <span style="background-color: #d1fae5; color: #065f46; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: bold;">
                    ${category.status.charAt(0).toUpperCase() + category.status.slice(1)}
                </span>
            </td>
            <td style="padding: 12px;">
                <div style="display: flex; gap: 8px;">
                    <button onclick="editCategory('${category.code}')" style="background-color: #2563eb; color: white; border: none; border-radius: 4px; padding: 4px 8px; font-size: 12px; font-weight: bold; cursor: pointer;">
                        Edit
                    </button>
                    <button onclick="deleteCategory('${category.code}')" style="background-color: #dc2626; color: white; border: none; border-radius: 4px; padding: 4px 8px; font-size: 12px; font-weight: bold; cursor: pointer;">
                        Delete
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Location Management Functions
let locations = [
    {
        locationCode: "HU",
        location: "Huntington",
        companyName: "Love Toys",
        address1: "700 Jericho Turnpike",
        address2: "Huntington Station, NY 11746",
        phone: "1631 923 ...",
        taxPercent: "8.625"
    },
    {
        locationCode: "PC",
        location: "LOVE TOYS",
        companyName: "Pacific Av...",
        address1: "1626 Pacific Ave",
        address2: "Atlantic city, N...",
        phone: "**********",
        taxPercent: "6.625"
    },
    {
        locationCode: "RS",
        location: "Rainbow St...",
        companyName: "RAINBO...",
        address1: "203 8th Ave",
        address2: "New York,NY 1...",
        phone: "212-206-7...",
        taxPercent: "8.875"
    }
];

function initializeLocationPage() {
    renderLocationTable();
}

function getLocationFormData() {
    return {
        locationCode: document.getElementById('locationCode').value,
        location: document.getElementById('location').value,
        companyName: document.getElementById('companyName').value,
        address1: document.getElementById('address1').value,
        address2: document.getElementById('address2').value,
        phone: document.getElementById('phone').value,
        taxPercent: document.getElementById('taxPercent').value,
        email: document.getElementById('email').value,
        appMode: document.getElementById('appMode').value,
        theaterPLU: document.getElementById('theaterPLU').value,
        theaterTime: document.getElementById('theaterTime').value,
        deli: document.getElementById('deli').checked
    };
}

function setLocationFormData(data) {
    document.getElementById('locationCode').value = data.locationCode || '';
    document.getElementById('location').value = data.location || '';
    document.getElementById('companyName').value = data.companyName || '';
    document.getElementById('address1').value = data.address1 || '';
    document.getElementById('address2').value = data.address2 || '';
    document.getElementById('phone').value = data.phone || '';
    document.getElementById('taxPercent').value = data.taxPercent || '';
    document.getElementById('email').value = data.email || '';
    document.getElementById('appMode').value = data.appMode || '';
    document.getElementById('theaterPLU').value = data.theaterPLU || '';
    document.getElementById('theaterTime').value = data.theaterTime || '';
    document.getElementById('deli').checked = data.deli || false;
}

function saveLocation() {
    const data = getLocationFormData();

    if (!data.locationCode || !data.location) {
        alert('Please fill in required fields: Location Code and Location');
        return;
    }

    // Check if location code already exists
    const existingIndex = locations.findIndex(loc => loc.locationCode === data.locationCode);

    if (existingIndex >= 0) {
        // Update existing location
        locations[existingIndex] = {
            locationCode: data.locationCode,
            location: data.location,
            companyName: data.companyName,
            address1: data.address1,
            address2: data.address2,
            phone: data.phone,
            taxPercent: data.taxPercent
        };
    } else {
        // Add new location
        locations.push({
            locationCode: data.locationCode,
            location: data.location,
            companyName: data.companyName,
            address1: data.address1,
            address2: data.address2,
            phone: data.phone,
            taxPercent: data.taxPercent
        });
    }

    renderLocationTable();
    console.log('Location saved:', data);
    alert('Location saved successfully!');
}

function clearLocationForm() {
    setLocationFormData({});
    console.log('Form cleared');
}

function deleteLocation() {
    const locationCode = document.getElementById('locationCode').value;

    if (!locationCode) {
        alert('Please select a location to delete');
        return;
    }

    if (confirm('Are you sure you want to delete this location?')) {
        locations = locations.filter(loc => loc.locationCode !== locationCode);
        renderLocationTable();
        clearLocationForm();
        console.log('Location deleted:', locationCode);
    }
}

function selectLocation(locationCode) {
    const location = locations.find(loc => loc.locationCode === locationCode);
    if (location) {
        setLocationFormData(location);
    }
}

function renderLocationTable() {
    const tbody = document.getElementById('locationTableBody');
    if (!tbody) return;

    tbody.innerHTML = '';

    locations.forEach((location, index) => {
        const row = document.createElement('tr');
        row.onclick = () => selectLocation(location.locationCode);
        row.style.cssText = `border-bottom: 1px solid #e5e7eb; cursor: pointer; ${index % 2 === 0 ? 'background-color: white;' : 'background-color: #f9fafb;'}`;
        row.onmouseover = () => row.style.backgroundColor = '#f3f4f6';
        row.onmouseout = () => row.style.backgroundColor = index % 2 === 0 ? 'white' : '#f9fafb';

        row.innerHTML = `
            <td style="padding: 12px; font-weight: 500; color: #1f2937; border-right: 1px solid #e5e7eb;">${location.locationCode}</td>
            <td style="padding: 12px; color: #1f2937; border-right: 1px solid #e5e7eb;">${location.location}</td>
            <td style="padding: 12px; color: #1f2937; border-right: 1px solid #e5e7eb;">${location.companyName}</td>
            <td style="padding: 12px; color: #1f2937; border-right: 1px solid #e5e7eb;">${location.address1}</td>
            <td style="padding: 12px; color: #1f2937; border-right: 1px solid #e5e7eb;">${location.address2}</td>
            <td style="padding: 12px; color: #1f2937; text-align: center; border-right: 1px solid #e5e7eb;">${location.phone}</td>
            <td style="padding: 12px; color: #1f2937; text-align: center;">${location.taxPercent}</td>
        `;

        tbody.appendChild(row);
    });
}

// Supplier Management Functions
let suppliers = [
    {
        supplierName: "505 INC Distribution center",
        telephone: "",
        fax: "",
        email: "",
        salesRep: "",
        repPhone: ""
    },
    {
        supplierName: "ALLEN BROTHERS",
        telephone: "",
        fax: "",
        email: "",
        salesRep: "",
        repPhone: ""
    },
    {
        supplierName: "Baci",
        telephone: "",
        fax: "",
        email: "",
        salesRep: "",
        repPhone: ""
    },
    {
        supplierName: "Bamboo Magic",
        telephone: "",
        fax: "",
        email: "",
        salesRep: "",
        repPhone: ""
    },
    {
        supplierName: "BE WICKED!",
        telephone: "",
        fax: "",
        email: "",
        salesRep: "",
        repPhone: ""
    },
    {
        supplierName: "Black Unicorn",
        telephone: "",
        fax: "",
        email: "",
        salesRep: "",
        repPhone: ""
    },
    {
        supplierName: "BLUE OX DESIGNS LLC",
        telephone: "",
        fax: "",
        email: "",
        salesRep: "",
        repPhone: ""
    },
    {
        supplierName: "Boy butter",
        telephone: "",
        fax: "",
        email: "",
        salesRep: "",
        repPhone: ""
    },
    {
        supplierName: "Cents Price",
        telephone: "",
        fax: "",
        email: "",
        salesRep: "",
        repPhone: ""
    }
];

let selectedSupplierIndex = -1;

function initializeSupplierPage() {
    renderSupplierTable();
}

function getSupplierFormData() {
    return {
        name: document.getElementById('supplierName').value,
        address1: document.getElementById('supplierAddress1').value,
        address2: document.getElementById('supplierAddress2').value,
        city: document.getElementById('supplierCity').value,
        state: document.getElementById('supplierState').value,
        zipCode: document.getElementById('supplierZipCode').value,
        telephone: document.getElementById('supplierTelephone').value,
        fax: document.getElementById('supplierFax').value,
        email: document.getElementById('supplierEmail').value,
        salesRep: document.getElementById('supplierSalesRep').value,
        salesRepPhone: document.getElementById('supplierSalesRepPhone').value,
        retailWebsite: document.getElementById('supplierRetailWebsite').value,
        wholesaleWebsite: document.getElementById('supplierWholesaleWebsite').value,
        username: document.getElementById('supplierUsername').value,
        password: document.getElementById('supplierPassword').value
    };
}

function setSupplierFormData(data) {
    document.getElementById('supplierName').value = data.name || '';
    document.getElementById('supplierAddress1').value = data.address1 || '';
    document.getElementById('supplierAddress2').value = data.address2 || '';
    document.getElementById('supplierCity').value = data.city || '';
    document.getElementById('supplierState').value = data.state || '';
    document.getElementById('supplierZipCode').value = data.zipCode || '';
    document.getElementById('supplierTelephone').value = data.telephone || '';
    document.getElementById('supplierFax').value = data.fax || '';
    document.getElementById('supplierEmail').value = data.email || '';
    document.getElementById('supplierSalesRep').value = data.salesRep || '';
    document.getElementById('supplierSalesRepPhone').value = data.salesRepPhone || '';
    document.getElementById('supplierRetailWebsite').value = data.retailWebsite || '';
    document.getElementById('supplierWholesaleWebsite').value = data.wholesaleWebsite || '';
    document.getElementById('supplierUsername').value = data.username || '';
    document.getElementById('supplierPassword').value = data.password || '';
}

function saveSupplier() {
    const data = getSupplierFormData();

    if (!data.name) {
        alert('Please fill in required field: Supplier Name');
        return;
    }

    if (selectedSupplierIndex >= 0) {
        // Update existing supplier
        suppliers[selectedSupplierIndex] = {
            supplierName: data.name,
            telephone: data.telephone,
            fax: data.fax,
            email: data.email,
            salesRep: data.salesRep,
            repPhone: data.salesRepPhone
        };
    } else {
        // Check if supplier already exists
        const existingIndex = suppliers.findIndex(sup => sup.supplierName === data.name);

        if (existingIndex >= 0) {
            // Update existing supplier
            suppliers[existingIndex] = {
                supplierName: data.name,
                telephone: data.telephone,
                fax: data.fax,
                email: data.email,
                salesRep: data.salesRep,
                repPhone: data.salesRepPhone
            };
        } else {
            // Add new supplier
            suppliers.push({
                supplierName: data.name,
                telephone: data.telephone,
                fax: data.fax,
                email: data.email,
                salesRep: data.salesRep,
                repPhone: data.salesRepPhone
            });
        }
    }

    renderSupplierTable();
    console.log('Supplier saved:', data);
    alert('Supplier saved successfully!');
}

function clearSupplierForm() {
    setSupplierFormData({});
    selectedSupplierIndex = -1;

    // Clear table selection
    const rows = document.querySelectorAll('#supplierTableBody tr');
    rows.forEach(row => row.classList.remove('selected'));

    console.log('Form cleared');
}

function deleteSupplier() {
    const supplierName = document.getElementById('supplierName').value;

    if (!supplierName) {
        alert('Please select a supplier to delete');
        return;
    }

    if (confirm('Are you sure you want to delete this supplier?')) {
        suppliers = suppliers.filter(sup => sup.supplierName !== supplierName);
        renderSupplierTable();
        clearSupplierForm();
        console.log('Supplier deleted:', supplierName);
    }
}

function selectSupplier(index) {
    selectedSupplierIndex = index;
    const supplier = suppliers[index];

    if (supplier) {
        setSupplierFormData({
            name: supplier.supplierName,
            telephone: supplier.telephone,
            fax: supplier.fax,
            email: supplier.email,
            salesRep: supplier.salesRep,
            salesRepPhone: supplier.repPhone
        });

        // Update table selection
        const rows = document.querySelectorAll('#supplierTableBody tr');
        rows.forEach((row, i) => {
            if (i === index) {
                row.style.backgroundColor = '#dbeafe';
            } else {
                row.style.backgroundColor = i % 2 === 0 ? 'white' : '#f9fafb';
            }
        });
    }
}

function renderSupplierTable() {
    const tbody = document.getElementById('supplierTableBody');
    if (!tbody) return;

    tbody.innerHTML = '';

    suppliers.forEach((supplier, index) => {
        const row = document.createElement('tr');
        row.onclick = () => selectSupplier(index);
        row.style.cssText = `border-bottom: 1px solid #e5e7eb; cursor: pointer; ${index % 2 === 0 ? 'background-color: white;' : 'background-color: #f9fafb;'}`;
        row.onmouseover = () => row.style.backgroundColor = '#f3f4f6';
        row.onmouseout = () => row.style.backgroundColor = index % 2 === 0 ? 'white' : '#f9fafb';

        row.innerHTML = `
            <td style="padding: 12px; font-weight: 500; color: #1f2937; border-right: 1px solid #e5e7eb;">${supplier.supplierName}</td>
            <td style="padding: 12px; color: #1f2937; text-align: center; border-right: 1px solid #e5e7eb;">${supplier.telephone || '-'}</td>
            <td style="padding: 12px; color: #1f2937; text-align: center; border-right: 1px solid #e5e7eb;">${supplier.fax || '-'}</td>
            <td style="padding: 12px; color: #1f2937; text-align: center; border-right: 1px solid #e5e7eb;">${supplier.email || '-'}</td>
            <td style="padding: 12px; color: #1f2937; text-align: center; border-right: 1px solid #e5e7eb;">${supplier.salesRep || '-'}</td>
            <td style="padding: 12px; color: #1f2937; text-align: center;">${supplier.repPhone || '-'}</td>
        `;

        tbody.appendChild(row);
    });
}

// Product Management Functions
let productLocationStocks = [
    { location: "Taboo-Atlantic City-NJ", stock: 0, price: 0.00 },
    { location: "Huntington", stock: 0, price: 0.00 },
    { location: "Miami Playground-Florida", stock: 0, price: 0.00 },
    { location: "LOVE TOYS", stock: 0, price: 0.00 },
    { location: "Pompano Beach-Florida", stock: 0, price: 0.00 },
    { location: "Rainbow Station-New York", stock: 0, price: 0.00 }
];

function initializeProductPage() {
    renderProductLocationStockTable();
}

function getProductFormData() {
    return {
        barcode: document.getElementById('productBarcode').value,
        description: document.getElementById('productDescription').value,
        category: document.getElementById('productCategory').value,
        subCategory: document.getElementById('productSubCategory').value,
        supplier: document.getElementById('productSupplier').value,
        purchasePrice: document.getElementById('productPurchasePrice').value,
        style: document.getElementById('productStyle').value,
        color: document.getElementById('productColor').value,
        size: document.getElementById('productSize').value,
        minQty: document.getElementById('productMinQty').value,
        maxQty: document.getElementById('productMaxQty').value,
        specialDiscount: document.getElementById('productSpecialDiscount').checked,
        priority: document.getElementById('productPriority').checked,
        imageConfirm: document.getElementById('productImageConfirm').checked,
        nonScanable: document.getElementById('productNonScanable').checked,
        deliItem: document.getElementById('productDeliItem').checked
    };
}

function setProductFormData(data) {
    document.getElementById('productBarcode').value = data.barcode || '';
    document.getElementById('productDescription').value = data.description || '';
    document.getElementById('productCategory').value = data.category || '';
    document.getElementById('productSubCategory').value = data.subCategory || '';
    document.getElementById('productSupplier').value = data.supplier || '';
    document.getElementById('productPurchasePrice').value = data.purchasePrice || '';
    document.getElementById('productStyle').value = data.style || '';
    document.getElementById('productColor').value = data.color || '';
    document.getElementById('productSize').value = data.size || '';
    document.getElementById('productMinQty').value = data.minQty || '';
    document.getElementById('productMaxQty').value = data.maxQty || '';
    document.getElementById('productSpecialDiscount').checked = data.specialDiscount || false;
    document.getElementById('productPriority').checked = data.priority || false;
    document.getElementById('productImageConfirm').checked = data.imageConfirm || false;
    document.getElementById('productNonScanable').checked = data.nonScanable || false;
    document.getElementById('productDeliItem').checked = data.deliItem || false;
}

function saveProduct() {
    const data = getProductFormData();

    if (!data.barcode || !data.description) {
        alert('Please fill in required fields: Barcode and Description');
        return;
    }

    // Get location stock data
    const stockData = [];
    const stockInputs = document.querySelectorAll('#productLocationStockTable input[data-type="stock"]');
    const priceInputs = document.querySelectorAll('#productLocationStockTable input[data-type="price"]');

    stockInputs.forEach((input, index) => {
        stockData.push({
            location: productLocationStocks[index].location,
            stock: parseInt(input.value) || 0,
            price: parseFloat(priceInputs[index].value) || 0.00
        });
    });

    const productData = {
        ...data,
        locationStocks: stockData
    };

    console.log('Product saved:', productData);
    alert('Product saved successfully!');
}

function clearProductForm() {
    setProductFormData({});

    // Clear location stock table
    const stockInputs = document.querySelectorAll('#productLocationStockTable input[data-type="stock"]');
    const priceInputs = document.querySelectorAll('#productLocationStockTable input[data-type="price"]');

    stockInputs.forEach(input => input.value = '0');
    priceInputs.forEach(input => input.value = '0.00');

    console.log('Form cleared');
}

function deleteProduct() {
    const barcode = document.getElementById('productBarcode').value;

    if (!barcode) {
        alert('Please enter a barcode to delete');
        return;
    }

    if (confirm('Are you sure you want to delete this product?')) {
        clearProductForm();
        console.log('Product deleted:', barcode);
    }
}

function renderProductLocationStockTable() {
    const tbody = document.getElementById('productLocationStockTable');
    if (!tbody) return;

    tbody.innerHTML = '';

    productLocationStocks.forEach((location, index) => {
        const row = document.createElement('tr');
        row.style.cssText = `border-bottom: 1px solid #e5e7eb; ${index % 2 === 0 ? 'background-color: white;' : 'background-color: #f9fafb;'}`;

        row.innerHTML = `
            <td style="padding: 12px; font-weight: 500; color: #1f2937; border-right: 1px solid #d1d5db;">${location.location}</td>
            <td style="padding: 8px 12px; text-align: center; border-right: 1px solid #d1d5db;">
                <input type="number" data-type="stock" style="width: 80px; text-align: center; padding: 4px 8px; border: 1px solid #d1d5db; border-radius: 4px;" value="${location.stock}" placeholder="0">
            </td>
            <td style="padding: 8px 12px; text-align: center;">
                <input type="number" step="0.01" data-type="price" style="width: 96px; text-align: center; padding: 4px 8px; border: 1px solid #d1d5db; border-radius: 4px;" value="${location.price.toFixed(2)}" placeholder="0.00">
            </td>
        `;

        tbody.appendChild(row);
    });

    // Add event listeners for input changes
    const stockInputs = document.querySelectorAll('#productLocationStockTable input[data-type="stock"]');
    const priceInputs = document.querySelectorAll('#productLocationStockTable input[data-type="price"]');

    stockInputs.forEach((input, index) => {
        input.addEventListener('input', function() {
            productLocationStocks[index].stock = parseInt(this.value) || 0;
        });
    });

    priceInputs.forEach((input, index) => {
        input.addEventListener('input', function() {
            productLocationStocks[index].price = parseFloat(this.value) || 0.00;
        });
    });
}

// User Management Functions
let allUsers = [];
let filteredUsers = [];

async function initializeUserManagement() {
    await loadAllUsers();
    updateUserCounts();
    applyFilters(); // Initialize with all users
    setupFilterEventListeners(); // Setup event listeners for filters
}

function setupFilterEventListeners() {
    // Setup search input event listener
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        // Ensure the input is focusable and editable
        searchInput.removeAttribute('readonly');
        searchInput.removeAttribute('disabled');
        searchInput.style.pointerEvents = 'auto';
        searchInput.style.userSelect = 'text';

        // Add event listeners
        searchInput.addEventListener('input', applyFilters);
        searchInput.addEventListener('keyup', applyFilters);
    }

    // Setup filter dropdown event listeners
    const roleFilter = document.getElementById('roleFilter');
    if (roleFilter) {
        roleFilter.addEventListener('change', applyFilters);
    }

    const statusFilter = document.getElementById('statusFilter');
    if (statusFilter) {
        statusFilter.addEventListener('change', applyFilters);
    }

    const permissionFilter = document.getElementById('permissionFilter');
    if (permissionFilter) {
        permissionFilter.addEventListener('change', applyFilters);
    }
}



function updateUserCounts() {
    const adminCount = allUsers.filter(user => user.role === 'Admin' && user.status === 'active').length;
    const cashierCount = allUsers.filter(user => user.role === 'Cashier' && user.status === 'active').length;
    const cctvCount = allUsers.filter(user => user.role === 'CCTV' && user.status === 'active').length;

    const adminCountEl = document.getElementById('admin-count');
    const cashierCountEl = document.getElementById('cashier-count');
    const cctvCountEl = document.getElementById('cctv-count');

    if (adminCountEl) adminCountEl.textContent = adminCount;
    if (cashierCountEl) cashierCountEl.textContent = cashierCount;
    if (cctvCountEl) cctvCountEl.textContent = cctvCount;
}

function renderUserTable() {
    const tableBody = document.getElementById('userTableBody');
    if (!tableBody) return;

    const usersToRender = filteredUsers.length > 0 || hasActiveFilters() ? filteredUsers : allUsers;

    if (usersToRender.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="7" style="padding: 24px; text-align: center; color: #6b7280; font-style: italic;">
                    ${hasActiveFilters() ? 'No users match the current filters.' : 'No users found.'}
                </td>
            </tr>
        `;
        return;
    }

    tableBody.innerHTML = usersToRender.map(user => {
        const roleColor = getRoleColor(user.role);
        const statusColor = user.status === 'active' ? '#d1fae5' : '#fee2e2';
        const statusTextColor = user.status === 'active' ? '#065f46' : '#991b1b';
        const permissions = getPermissionText(user.role);
        const lastLogin = user.last_login ? formatDateTime(user.last_login) : 'Never';

        return `
            <tr style="border-bottom: 1px solid #f3f4f6;">
                <td style="padding: 12px; color: #1f2937; font-weight: 500;">${user.username}</td>
                <td style="padding: 12px; color: #1f2937;">${user.name || 'N/A'}</td>
                <td style="padding: 12px;">
                    <span style="background-color: ${roleColor.bg}; color: ${roleColor.text}; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: bold;">
                        ${user.role}
                    </span>
                </td>
                <td style="padding: 12px;">
                    <span style="background-color: ${statusColor}; color: ${statusTextColor}; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: bold;">
                        ${user.status.charAt(0).toUpperCase() + user.status.slice(1)}
                    </span>
                </td>
                <td style="padding: 12px; color: #6b7280;">${lastLogin}</td>
                <td style="padding: 12px; color: #6b7280;">${permissions}</td>
                <td style="padding: 12px;">
                    <div style="display: flex; gap: 4px;">
                        <button onclick="editUser(${user.id})" style="background-color: #2563eb; color: white; border: none; border-radius: 4px; padding: 4px 8px; font-size: 12px; font-weight: bold; cursor: pointer;">
                            Edit
                        </button>
                        <button onclick="deleteUser(${user.id})" style="background-color: #dc2626; color: white; border: none; border-radius: 4px; padding: 4px 8px; font-size: 12px; font-weight: bold; cursor: pointer;">
                            Delete
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

function getRoleColor(role) {
    switch (role) {
        case 'Admin':
            return { bg: '#d1fae5', text: '#065f46' };
        case 'Cashier':
            return { bg: '#dbeafe', text: '#1e40af' };
        case 'CCTV':
            return { bg: '#fee2e2', text: '#991b1b' };
        default:
            return { bg: '#f3f4f6', text: '#374151' };
    }
}

function getPermissionText(role) {
    switch (role) {
        case 'Admin':
            return 'Full Access';
        case 'Cashier':
            return 'POS, Theater';
        case 'CCTV':
            return 'View Only';
        default:
            return 'Limited';
    }
}

function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
    });
}

// Force create permissions table
async function forceCreatePermissionsTable() {
    try {
        const result = await ipcRenderer.invoke('force-create-permissions-table');
        if (result.success) {
            alert('Permissions table created successfully!');
        } else {
            alert('Error creating permissions table: ' + result.message);
        }
    } catch (error) {
        console.error('Error creating permissions table:', error);
        alert('Error creating permissions table. Check console for details.');
    }
}

// Debug function to check user permissions in database
async function debugUserPermissions() {
    const username = prompt('Enter username to debug (e.g., saman):');
    if (!username) return;

    try {
        const result = await ipcRenderer.invoke('debug-user-permissions', username);
        if (result.success) {
            console.log('=== DATABASE DEBUG RESULTS ===');
            console.log('User from database:', result.user);
            console.log('Permissions from database:', result.permissions);
            console.log('=== END DATABASE DEBUG ===');

            alert(`Debug results logged to console. User: ${result.user?.name}, Permissions: ${result.permissions?.length || 0}`);
        } else {
            alert('Error: ' + result.message);
        }
    } catch (error) {
        console.error('Error debugging user permissions:', error);
        alert('Error debugging user permissions. Check console for details.');
    }
}

// Define available modules for access control
const availableModules = [
    { id: 'pos', name: 'Point of Sale System', category: 'main' },
    { id: 'theater', name: 'Theater Management System', category: 'main' },
    { id: 'dashboard', name: 'Dashboard', category: 'admin' },
    { id: 'master', name: 'Master Data', category: 'admin' },
    { id: 'setup-location', name: 'Set Up Location', category: 'master' },
    { id: 'setup-category', name: 'Set Up Category', category: 'master' },
    { id: 'setup-supplier', name: 'Set Up Supplier', category: 'master' },
    { id: 'setup-product', name: 'Set Up Product', category: 'master' },
    { id: 'reports', name: 'Reports', category: 'admin' },
    { id: 'transactions', name: 'Transactions', category: 'admin' },
    { id: 'wholesale', name: 'Wholesale', category: 'admin' },
    { id: 'user-management', name: 'User Management', category: 'admin' }
];

function toggleAccessControl() {
    const roleSelect = document.getElementById('userRole');
    const accessControlSection = document.getElementById('accessControlSection');

    if (roleSelect.value === 'Cashier' || roleSelect.value === 'CCTV') {
        accessControlSection.style.display = 'block';
        generateAccessControlList();
    } else {
        accessControlSection.style.display = 'none';
    }
}

function generateAccessControlList() {
    const accessControlList = document.getElementById('accessControlList');

    let html = '';

    // Group modules by category
    const categories = {
        'main': 'Main Systems',
        'admin': 'Admin Panel',
        'master': 'Master Data'
    };

    Object.keys(categories).forEach(categoryKey => {
        const categoryModules = availableModules.filter(module => module.category === categoryKey);

        if (categoryModules.length > 0) {
            html += `
                <div style="margin-bottom: 16px;">
                    <h5 style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; border-bottom: 1px solid #e5e7eb; padding-bottom: 4px;">
                        ${categories[categoryKey]}
                    </h5>
            `;

            categoryModules.forEach(module => {
                html += `
                    <div style="margin-bottom: 12px; padding: 8px; border: 1px solid #e5e7eb; border-radius: 6px; background-color: white;">
                        <div style="display: flex; align-items: center; margin-bottom: 8px;">
                            <input type="checkbox" id="module_${module.id}" onchange="toggleModulePermissions('${module.id}')" style="margin-right: 8px;">
                            <label for="module_${module.id}" style="font-weight: bold; color: #374151; cursor: pointer;">${module.name}</label>
                        </div>
                        <div id="permissions_${module.id}" style="display: none; margin-left: 20px; display: flex; gap: 16px;">
                            <label style="display: flex; align-items: center; font-size: 12px; color: #6b7280;">
                                <input type="checkbox" id="view_${module.id}" style="margin-right: 4px;">
                                View
                            </label>
                            <label style="display: flex; align-items: center; font-size: 12px; color: #6b7280;">
                                <input type="checkbox" id="edit_${module.id}" style="margin-right: 4px;">
                                Edit
                            </label>
                            <label style="display: flex; align-items: center; font-size: 12px; color: #6b7280;">
                                <input type="checkbox" id="delete_${module.id}" style="margin-right: 4px;">
                                Delete
                            </label>
                            <label style="display: flex; align-items: center; font-size: 12px; color: #059669; font-weight: bold;">
                                <input type="checkbox" id="all_${module.id}" onchange="toggleAllPermissions('${module.id}')" style="margin-right: 4px;">
                                All Operations
                            </label>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
        }
    });

    accessControlList.innerHTML = html;
}

function toggleModulePermissions(moduleId) {
    const moduleCheckbox = document.getElementById(`module_${moduleId}`);
    const permissionsDiv = document.getElementById(`permissions_${moduleId}`);

    if (moduleCheckbox.checked) {
        permissionsDiv.style.display = 'flex';
        // Auto-check view permission when module is selected
        document.getElementById(`view_${moduleId}`).checked = true;
    } else {
        permissionsDiv.style.display = 'none';
        // Uncheck all permissions when module is deselected
        document.getElementById(`view_${moduleId}`).checked = false;
        document.getElementById(`edit_${moduleId}`).checked = false;
        document.getElementById(`delete_${moduleId}`).checked = false;
        document.getElementById(`all_${moduleId}`).checked = false;
    }
}

function toggleAllPermissions(moduleId) {
    const allCheckbox = document.getElementById(`all_${moduleId}`);
    const viewCheckbox = document.getElementById(`view_${moduleId}`);
    const editCheckbox = document.getElementById(`edit_${moduleId}`);
    const deleteCheckbox = document.getElementById(`delete_${moduleId}`);

    if (allCheckbox.checked) {
        viewCheckbox.checked = true;
        editCheckbox.checked = true;
        deleteCheckbox.checked = true;
    } else {
        viewCheckbox.checked = true; // Keep view checked as minimum
        editCheckbox.checked = false;
        deleteCheckbox.checked = false;
    }
}

function showAddUserModal(role) {
    const modal = document.getElementById('addUserModal');
    const modalTitle = document.getElementById('modalTitle');
    const roleSelect = document.getElementById('userRole');

    if (modal && modalTitle && roleSelect) {
        modalTitle.textContent = `Add New ${role} User`;
        roleSelect.value = role;
        modal.style.display = 'flex';

        // Clear form
        document.getElementById('userName').value = '';
        document.getElementById('userUsername').value = '';
        document.getElementById('userPassword').value = '';

        // Toggle access control based on role
        toggleAccessControl();
    }
}

function closeAddUserModal() {
    const modal = document.getElementById('addUserModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

function collectUserPermissions() {
    const permissions = [];
    const role = document.getElementById('userRole').value;

    // Only collect permissions for Cashier and CCTV roles
    if (role === 'Cashier' || role === 'CCTV') {
        availableModules.forEach(module => {
            const moduleCheckbox = document.getElementById(`module_${module.id}`);

            if (moduleCheckbox && moduleCheckbox.checked) {
                const viewCheckbox = document.getElementById(`view_${module.id}`);
                const editCheckbox = document.getElementById(`edit_${module.id}`);
                const deleteCheckbox = document.getElementById(`delete_${module.id}`);

                permissions.push({
                    module_id: module.id,
                    module_name: module.name,
                    can_view: viewCheckbox ? viewCheckbox.checked : false,
                    can_edit: editCheckbox ? editCheckbox.checked : false,
                    can_delete: deleteCheckbox ? deleteCheckbox.checked : false
                });
            }
        });
    }

    return permissions;
}

async function handleAddUser(event) {
    event.preventDefault();

    const userData = {
        name: document.getElementById('userName').value,
        username: document.getElementById('userUsername').value,
        password: document.getElementById('userPassword').value,
        role: document.getElementById('userRole').value
    };

    try {
        // First create the user
        const result = await ipcRenderer.invoke('create-user', userData);
        if (result.success) {
            const userId = result.user.id;

            // Then save permissions if it's a Cashier or CCTV role
            const permissions = collectUserPermissions();
            if (permissions.length > 0) {
                const permissionResult = await ipcRenderer.invoke('create-user-permissions', userId, permissions);
                if (!permissionResult.success) {
                    console.error('Error saving permissions:', permissionResult.message);
                    alert('User created but permissions could not be saved: ' + permissionResult.message);
                    return;
                }
            }

            alert('User created successfully with permissions!');
            closeAddUserModal();
            await loadAllUsers();
            updateUserCounts();
            applyFilters();
        } else {
            alert('Error creating user: ' + result.message);
        }
    } catch (error) {
        console.error('Error creating user:', error);
        alert('Error creating user. Please try again.');
    }
}

async function editUser(userId) {
    try {
        const result = await ipcRenderer.invoke('get-user-by-id', userId);
        if (result.success && result.user) {
            const user = result.user;

            // Populate edit form
            document.getElementById('editUserId').value = user.id;
            document.getElementById('editUserName').value = user.name || '';
            document.getElementById('editUserUsername').value = user.username;
            document.getElementById('editUserRole').value = user.role;
            document.getElementById('editUserStatus').value = user.status || 'active';
            document.getElementById('editUserPassword').value = ''; // Always empty for security

            // Handle access control for Cashier and CCTV roles
            await toggleEditAccessControl(user.role, userId);

            // Show edit modal
            const modal = document.getElementById('editUserModal');
            if (modal) {
                modal.style.display = 'flex';
            }
        } else {
            alert('Error loading user data: ' + (result.message || 'User not found'));
        }
    } catch (error) {
        console.error('Error loading user for edit:', error);
        alert('Error loading user data. Please try again.');
    }
}

function closeEditUserModal() {
    const modal = document.getElementById('editUserModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// Toggle access control for edit user modal
async function toggleEditAccessControl(role, userId) {
    const accessControlSection = document.getElementById('editAccessControlSection');

    if (role === 'Cashier' || role === 'CCTV') {
        accessControlSection.style.display = 'block';
        await generateEditAccessControlList(userId);
    } else {
        accessControlSection.style.display = 'none';
    }
}

// Generate access control list for edit modal with existing permissions
async function generateEditAccessControlList(userId) {
    const accessControlList = document.getElementById('editAccessControlList');

    // Load existing user permissions
    let existingPermissions = [];
    try {
        const result = await ipcRenderer.invoke('get-user-permissions', userId);
        if (result.success) {
            existingPermissions = result.permissions;
            console.log('Loaded existing permissions for user:', existingPermissions);
        }
    } catch (error) {
        console.error('Error loading user permissions:', error);
    }

    let html = '';

    // Group modules by category
    const categories = {
        'main': 'Main Systems',
        'admin': 'Admin Panel',
        'master': 'Master Data'
    };

    Object.keys(categories).forEach(categoryKey => {
        const categoryModules = availableModules.filter(module => module.category === categoryKey);

        if (categoryModules.length > 0) {
            html += `
                <div style="margin-bottom: 16px;">
                    <h5 style="font-size: 14px; font-weight: bold; color: #374151; margin-bottom: 8px; border-bottom: 1px solid #e5e7eb; padding-bottom: 4px;">
                        ${categories[categoryKey]}
                    </h5>
            `;

            categoryModules.forEach(module => {
                const existingPerm = existingPermissions.find(p => p.module_id === module.id);
                const isModuleChecked = !!existingPerm;
                const canView = existingPerm ? existingPerm.can_view === 1 : false;
                const canEdit = existingPerm ? existingPerm.can_edit === 1 : false;
                const canDelete = existingPerm ? existingPerm.can_delete === 1 : false;

                html += `
                    <div style="margin-bottom: 12px; padding: 8px; border: 1px solid #e5e7eb; border-radius: 6px; background-color: white;">
                        <div style="display: flex; align-items: center; margin-bottom: 8px;">
                            <input type="checkbox" id="edit_module_${module.id}" ${isModuleChecked ? 'checked' : ''}
                                   onchange="toggleEditModulePermissions('${module.id}')"
                                   style="margin-right: 8px;">
                            <label for="edit_module_${module.id}" style="font-weight: bold; color: #374151; cursor: pointer;">${module.name}</label>
                        </div>
                        <div id="edit_permissions_${module.id}" style="margin-left: 20px; display: ${isModuleChecked ? 'flex' : 'none'}; gap: 16px;">
                            <label style="display: flex; align-items: center; font-size: 12px; color: #6b7280;">
                                <input type="checkbox" id="edit_view_${module.id}" ${canView ? 'checked' : ''} style="margin-right: 4px;">
                                View
                            </label>
                            <label style="display: flex; align-items: center; font-size: 12px; color: #6b7280;">
                                <input type="checkbox" id="edit_edit_${module.id}" ${canEdit ? 'checked' : ''} style="margin-right: 4px;">
                                Edit
                            </label>
                            <label style="display: flex; align-items: center; font-size: 12px; color: #6b7280;">
                                <input type="checkbox" id="edit_delete_${module.id}" ${canDelete ? 'checked' : ''} style="margin-right: 4px;">
                                Delete
                            </label>
                            <label style="display: flex; align-items: center; font-size: 12px; color: #059669; font-weight: bold;">
                                <input type="checkbox" id="edit_all_${module.id}" onchange="toggleEditAllPermissions('${module.id}')" style="margin-right: 4px;">
                                All Operations
                            </label>
                        </div>
                    </div>
                `;
            });

            html += `</div>`;
        }
    });

    accessControlList.innerHTML = html;
}

// Helper functions for edit access control
function toggleEditModulePermissions(moduleId) {
    const moduleCheckbox = document.getElementById(`edit_module_${moduleId}`);
    const permissionsDiv = document.getElementById(`edit_permissions_${moduleId}`);

    if (moduleCheckbox.checked) {
        permissionsDiv.style.display = 'flex';
        // Auto-check view permission when module is selected
        document.getElementById(`edit_view_${moduleId}`).checked = true;
    } else {
        permissionsDiv.style.display = 'none';
        // Uncheck all permissions when module is deselected
        document.getElementById(`edit_view_${moduleId}`).checked = false;
        document.getElementById(`edit_edit_${moduleId}`).checked = false;
        document.getElementById(`edit_delete_${moduleId}`).checked = false;
        document.getElementById(`edit_all_${moduleId}`).checked = false;
    }
}

function toggleEditAllPermissions(moduleId) {
    const allCheckbox = document.getElementById(`edit_all_${moduleId}`);
    const viewCheckbox = document.getElementById(`edit_view_${moduleId}`);
    const editCheckbox = document.getElementById(`edit_edit_${moduleId}`);
    const deleteCheckbox = document.getElementById(`edit_delete_${moduleId}`);

    if (allCheckbox.checked) {
        viewCheckbox.checked = true;
        editCheckbox.checked = true;
        deleteCheckbox.checked = true;
    } else {
        viewCheckbox.checked = false;
        editCheckbox.checked = false;
        deleteCheckbox.checked = false;
    }
}

// Collect permissions from edit form
function collectEditUserPermissions() {
    const permissions = [];
    const role = document.getElementById('editUserRole').value;

    // Only collect permissions for Cashier and CCTV roles
    if (role === 'Cashier' || role === 'CCTV') {
        availableModules.forEach(module => {
            const moduleCheckbox = document.getElementById(`edit_module_${module.id}`);

            if (moduleCheckbox && moduleCheckbox.checked) {
                const viewCheckbox = document.getElementById(`edit_view_${module.id}`);
                const editCheckbox = document.getElementById(`edit_edit_${module.id}`);
                const deleteCheckbox = document.getElementById(`edit_delete_${module.id}`);

                permissions.push({
                    module_id: module.id,
                    module_name: module.name,
                    can_view: viewCheckbox ? viewCheckbox.checked : false,
                    can_edit: editCheckbox ? editCheckbox.checked : false,
                    can_delete: deleteCheckbox ? deleteCheckbox.checked : false
                });
            }
        });
    }

    return permissions;
}

async function handleEditUser(event) {
    event.preventDefault();

    const userId = document.getElementById('editUserId').value;
    const userData = {
        username: document.getElementById('editUserUsername').value,
        role: document.getElementById('editUserRole').value,
        name: document.getElementById('editUserName').value,
        status: document.getElementById('editUserStatus').value
    };

    const newPassword = document.getElementById('editUserPassword').value;

    try {
        // Update user basic info
        const result = await ipcRenderer.invoke('update-user', userId, userData);
        if (result.success) {
            // Update password if provided
            if (newPassword.trim()) {
                const passwordResult = await ipcRenderer.invoke('update-user-password', userId, newPassword);
                if (!passwordResult.success) {
                    alert('User updated but password update failed: ' + passwordResult.message);
                    return;
                }
            }

            // Update permissions for Cashier and CCTV roles
            const permissions = collectEditUserPermissions();
            if (permissions.length > 0 || userData.role === 'Cashier' || userData.role === 'CCTV') {
                console.log('Updating permissions for user:', permissions);
                const permissionResult = await ipcRenderer.invoke('create-user-permissions', userId, permissions);
                if (!permissionResult.success) {
                    console.error('Error updating permissions:', permissionResult.message);
                    alert('User updated but permissions could not be saved: ' + permissionResult.message);
                    return;
                }
            }

            alert('User updated successfully!');
            closeEditUserModal();
            await loadAllUsers();
            updateUserCounts();
            applyFilters();
        } else {
            alert('Error updating user: ' + result.message);
        }
    } catch (error) {
        console.error('Error updating user:', error);
        alert('Error updating user. Please try again.');
    }
}

async function deleteUser(userId) {
    const user = allUsers.find(u => u.id === userId);
    if (!user) return;

    if (confirm(`Are you sure you want to delete user: ${user.username}?`)) {
        try {
            const result = await ipcRenderer.invoke('delete-user', userId);
            if (result.success) {
                alert('User deleted successfully!');
                await loadAllUsers();
                updateUserCounts();
                applyFilters();
            } else {
                alert('Error deleting user: ' + result.message);
            }
        } catch (error) {
            console.error('Error deleting user:', error);
            alert('Error deleting user. Please try again.');
        }
    }
}

// Filtering Functions
function applyFilters() {
    const searchTerm = document.getElementById('searchInput')?.value.toLowerCase() || '';
    const roleFilter = document.getElementById('roleFilter')?.value || '';
    const statusFilter = document.getElementById('statusFilter')?.value || '';
    const permissionFilter = document.getElementById('permissionFilter')?.value || '';

    filteredUsers = allUsers.filter(user => {
        // Search filter (name or username)
        const matchesSearch = !searchTerm ||
            (user.name && user.name.toLowerCase().includes(searchTerm)) ||
            user.username.toLowerCase().includes(searchTerm);

        // Role filter
        const matchesRole = !roleFilter || user.role === roleFilter;

        // Status filter
        const matchesStatus = !statusFilter || user.status === statusFilter;

        // Permission filter
        const userPermission = getPermissionText(user.role);
        const matchesPermission = !permissionFilter || userPermission === permissionFilter;

        return matchesSearch && matchesRole && matchesStatus && matchesPermission;
    });

    renderUserTable();
    updateFilterResultCount();
}

function hasActiveFilters() {
    const searchTerm = document.getElementById('searchInput')?.value || '';
    const roleFilter = document.getElementById('roleFilter')?.value || '';
    const statusFilter = document.getElementById('statusFilter')?.value || '';
    const permissionFilter = document.getElementById('permissionFilter')?.value || '';

    return searchTerm || roleFilter || statusFilter || permissionFilter;
}

function updateFilterResultCount() {
    const countElement = document.getElementById('filterResultCount');
    if (countElement) {
        const count = hasActiveFilters() ? filteredUsers.length : allUsers.length;
        countElement.textContent = count;

        // Update color based on results
        if (hasActiveFilters()) {
            countElement.style.color = count > 0 ? '#059669' : '#dc2626';
        } else {
            countElement.style.color = '#059669';
        }
    }
}

function clearAllFilters() {
    // Clear all filter inputs
    const searchInput = document.getElementById('searchInput');
    const roleFilter = document.getElementById('roleFilter');
    const statusFilter = document.getElementById('statusFilter');
    const permissionFilter = document.getElementById('permissionFilter');

    if (searchInput) searchInput.value = '';
    if (roleFilter) roleFilter.value = '';
    if (statusFilter) statusFilter.value = '';
    if (permissionFilter) permissionFilter.value = '';

    // Reset filtered users and re-render
    filteredUsers = [];
    applyFilters();
}

// Test function for search input
function testSearchInput() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.focus();
        searchInput.value = 'test';
        alert('Search input test: ' + searchInput.value);
        applyFilters();
    } else {
        alert('Search input not found!');
    }
}

// Enhanced user management functions
async function loadAllUsers() {
    try {
        const result = await ipcRenderer.invoke('get-all-users');
        if (result.success) {
            allUsers = result.users;
            renderUserTable();
            updateFilterResultCount();
        } else {
            console.error('Error loading users:', result.message);
        }
    } catch (error) {
        console.error('Error loading users:', error);
    }
}